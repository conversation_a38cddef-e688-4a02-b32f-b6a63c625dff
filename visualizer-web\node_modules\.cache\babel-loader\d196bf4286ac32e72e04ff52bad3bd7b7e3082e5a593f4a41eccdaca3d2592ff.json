{"ast": null, "code": "import { Selection } from \"./index.js\";\nimport selector from \"../selector.js\";\nexport default function (select) {\n  if (typeof select !== \"function\") select = selector(select);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n  return new Selection(subgroups, this._parents);\n}", "map": {"version": 3, "names": ["Selection", "selector", "select", "groups", "_groups", "m", "length", "subgroups", "Array", "j", "group", "n", "subgroup", "node", "subnode", "i", "call", "__data__", "_parents"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selection/select.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,YAAY;AACpC,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGD,QAAQ,CAACC,MAAM,CAAC;EAE3D,KAAK,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,SAAS,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAC9F,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,QAAQ,GAAGL,SAAS,CAACE,CAAC,CAAC,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC,EAAEE,IAAI,EAAEC,OAAO,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;MACtH,IAAI,CAACF,IAAI,GAAGH,KAAK,CAACK,CAAC,CAAC,MAAMD,OAAO,GAAGZ,MAAM,CAACc,IAAI,CAACH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAEF,CAAC,EAAEL,KAAK,CAAC,CAAC,EAAE;QAC/E,IAAI,UAAU,IAAIG,IAAI,EAAEC,OAAO,CAACG,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;QACxDL,QAAQ,CAACG,CAAC,CAAC,GAAGD,OAAO;MACvB;IACF;EACF;EAEA,OAAO,IAAId,SAAS,CAACO,SAAS,EAAE,IAAI,CAACW,QAAQ,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
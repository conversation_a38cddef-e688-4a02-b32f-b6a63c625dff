import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { useQuery } from '@tanstack/react-query';
import { analyticsApi, IndividualStats } from '../../services/api';
import RefreshIcon from '@mui/icons-material/Refresh';
import PeopleIcon from '@mui/icons-material/People';
import WomanIcon from '@mui/icons-material/Woman';
import ManIcon from '@mui/icons-material/Man';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';

// Color palette for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

interface IndividualData {
  id: string;
  tree_id: string;
  gender: string;
  age_group: string;
  country: string;
}

interface IndividualsResponse {
  individuals: IndividualData[];
  total: number;
  page: number;
  limit: number;
}

const Individuals: React.FC = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [genderFilter, setGenderFilter] = useState('');
  const [ageGroupFilter, setAgeGroupFilter] = useState('');
  const [countryFilter, setCountryFilter] = useState('');
  const [hasTreeFilter, setHasTreeFilter] = useState('');

  // Fetch individual statistics
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<IndividualStats>({
    queryKey: ['individualStats'],
    queryFn: analyticsApi.getIndividualStats,
    refetchOnWindowFocus: false,
  });

  // Fetch individual data with filters
  const {
    data: individualsData,
    isLoading: individualsLoading,
    error: individualsError,
    refetch: refetchIndividuals,
  } = useQuery<IndividualsResponse>({
    queryKey: ['individuals', page, rowsPerPage, genderFilter, ageGroupFilter, countryFilter, hasTreeFilter],
    queryFn: () => analyticsApi.getIndividuals({
      limit: rowsPerPage,
      offset: page * rowsPerPage,
      ...(genderFilter && { gender: genderFilter }),
      ...(ageGroupFilter && { age_group: ageGroupFilter }),
      ...(countryFilter && { country: countryFilter }),
      ...(hasTreeFilter && { has_tree: hasTreeFilter }),
    }),
    refetchOnWindowFocus: false,
  });

  const handleRefresh = () => {
    refetchStats();
    refetchIndividuals();
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const clearFilters = () => {
    setGenderFilter('');
    setAgeGroupFilter('');
    setCountryFilter('');
    setHasTreeFilter('');
    setPage(0);
  };

  // Prepare chart data
  const genderChartData = statsData?.gender_distribution?.map((item, index) => ({
    name: item.gender || 'Unknown',
    value: item.count,
    percentage: item.percentage,
    color: COLORS[index % COLORS.length],
  })) || [];

  const ageChartData = statsData?.age_distribution?.map(item => ({
    name: item.age_range,
    count: item.count,
    percentage: item.percentage,
  })) || [];

  if (statsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (statsError) {
    return (
      <Alert
        severity="error"
        action={
          <Chip label="Retry" onClick={handleRefresh} color="error" variant="outlined" />
        }
      >
        Failed to load individual statistics. Please check if the API server is running.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Individual Analysis
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Demographics, surname patterns, and individual profiles
          </Typography>
        </Box>
        <Tooltip title="Refresh Data">
          <IconButton onClick={handleRefresh} color="primary">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Overview */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Individuals</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {statsData?.total_individuals?.toLocaleString() || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <FamilyRestroomIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">With Trees</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {statsData?.with_trees?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {statsData?.tree_percentage?.toFixed(1) || 0}% of total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ManIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Male</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {genderChartData.find(g => g.name.toLowerCase() === 'male')?.value?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {genderChartData.find(g => g.name.toLowerCase() === 'male')?.percentage?.toFixed(1) || 0}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <WomanIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6">Female</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {genderChartData.find(g => g.name.toLowerCase() === 'female')?.value?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {genderChartData.find(g => g.name.toLowerCase() === 'female')?.percentage?.toFixed(1) || 0}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} mb={4}>
        {/* Gender Distribution */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Gender Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={genderChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name}: ${percentage?.toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {genderChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip formatter={(value: any) => [value?.toLocaleString(), 'Count']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Age Distribution */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Age Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={ageChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip formatter={(value: any) => [value?.toLocaleString(), 'Count']} />
                <Legend />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Filters Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filter Individuals
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Gender</InputLabel>
              <Select
                value={genderFilter}
                label="Gender"
                onChange={(e) => setGenderFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="Male">Male</MenuItem>
                <MenuItem value="Female">Female</MenuItem>
                <MenuItem value="Unknown">Unknown</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Age Group</InputLabel>
              <Select
                value={ageGroupFilter}
                label="Age Group"
                onChange={(e) => setAgeGroupFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="0-20">0-20</MenuItem>
                <MenuItem value="21-40">21-40</MenuItem>
                <MenuItem value="41-60">41-60</MenuItem>
                <MenuItem value="61-80">61-80</MenuItem>
                <MenuItem value="80+">80+</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              size="small"
              label="Country"
              value={countryFilter}
              onChange={(e) => setCountryFilter(e.target.value)}
              placeholder="Enter country"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Has Tree</InputLabel>
              <Select
                value={hasTreeFilter}
                label="Has Tree"
                onChange={(e) => setHasTreeFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="true">Yes</MenuItem>
                <MenuItem value="false">No</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              fullWidth
              size="small"
            >
              Clear Filters
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Box display="flex" gap={1}>
              <Chip
                label={`Total: ${individualsData?.total?.toLocaleString() || 0}`}
                color="primary"
                size="small"
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Individuals Table */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Tree ID</TableCell>
                <TableCell>Gender</TableCell>
                <TableCell>Age Group</TableCell>
                <TableCell>Country</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {individualsLoading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <CircularProgress size={40} />
                  </TableCell>
                </TableRow>
              ) : individualsError ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Alert severity="error">Failed to load individuals data</Alert>
                  </TableCell>
                </TableRow>
              ) : (
                individualsData?.individuals?.map((individual) => (
                  <TableRow key={individual.id} hover>
                    <TableCell>{individual.id}</TableCell>
                    <TableCell>
                      {individual.tree_id ? (
                        <Chip label={individual.tree_id} size="small" color="success" />
                      ) : (
                        <Chip label="No Tree" size="small" color="default" />
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={individual.gender || 'Unknown'}
                        size="small"
                        color={
                          individual.gender === 'Male' ? 'info' :
                          individual.gender === 'Female' ? 'secondary' : 'default'
                        }
                      />
                    </TableCell>
                    <TableCell>{individual.age_group || 'Unknown'}</TableCell>
                    <TableCell>{individual.country || 'Unknown'}</TableCell>
                  </TableRow>
                )) || []
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={individualsData?.total || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default Individuals;
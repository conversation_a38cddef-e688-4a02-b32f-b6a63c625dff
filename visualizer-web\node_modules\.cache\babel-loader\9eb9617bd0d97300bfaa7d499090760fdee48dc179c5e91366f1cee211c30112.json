{"ast": null, "code": "export default function (d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d)) || isNaN(z = +this._z.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n    node = this._root,\n    retainer,\n    previous,\n    next,\n    x0 = this._x0,\n    y0 = this._y0,\n    z0 = this._z0,\n    x1 = this._x1,\n    y1 = this._y1,\n    z1 = this._z1,\n    x,\n    y,\n    z,\n    xm,\n    ym,\n    zm,\n    right,\n    bottom,\n    deep,\n    i,\n    j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;else y1 = ym;\n    if (deep = z >= (zm = (z0 + z1) / 2)) z0 = zm;else z1 = zm;\n    if (!(parent = node, node = node[i = deep << 2 | bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[i + 1 & 7] || parent[i + 2 & 7] || parent[i + 3 & 7] || parent[i + 4 & 7] || parent[i + 5 & 7] || parent[i + 6 & 7] || parent[i + 7 & 7]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return next ? previous.next = next : delete previous.next, this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3] || parent[4] || parent[5] || parent[6] || parent[7]) && node === (parent[7] || parent[6] || parent[5] || parent[4] || parent[3] || parent[2] || parent[1] || parent[0]) && !node.length) {\n    if (retainer) retainer[j] = node;else this._root = node;\n  }\n  return this;\n}\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}", "map": {"version": 3, "names": ["d", "isNaN", "x", "_x", "call", "y", "_y", "z", "_z", "parent", "node", "_root", "retainer", "previous", "next", "x0", "_x0", "y0", "_y0", "z0", "_z0", "x1", "_x1", "y1", "_y1", "z1", "_z1", "xm", "ym", "zm", "right", "bottom", "deep", "i", "j", "length", "data", "removeAll", "n", "remove"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/remove.js"], "sourcesContent": ["export default function(d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d)) || isNaN(z = +this._z.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      y0 = this._y0,\n      z0 = this._z0,\n      x1 = this._x1,\n      y1 = this._y1,\n      z1 = this._z1,\n      x,\n      y,\n      z,\n      xm,\n      ym,\n      zm,\n      right,\n      bottom,\n      deep,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (deep = z >= (zm = (z0 + z1) / 2)) z0 = zm; else z1 = zm;\n    if (!(parent = node, node = node[i = deep << 2 | bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 7] || parent[(i + 2) & 7] || parent[(i + 3) & 7] || parent[(i + 4) & 7] || parent[(i + 5) & 7] || parent[(i + 6) & 7] || parent[(i + 7) & 7]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3] || parent[4] || parent[5] || parent[6] || parent[7])\n      && node === (parent[7] || parent[6] || parent[5] || parent[4] || parent[3] || parent[2] || parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,IAAIC,KAAK,CAACC,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC,IAAIC,KAAK,CAACI,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC,IAAIC,KAAK,CAACM,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACJ,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAE9H,IAAIS,MAAM;IACNC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,QAAQ;IACRC,QAAQ;IACRC,IAAI;IACJC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbxB,CAAC;IACDG,CAAC;IACDE,CAAC;IACDoB,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,KAAK;IACLC,MAAM;IACNC,IAAI;IACJC,CAAC;IACDC,CAAC;;EAEL;EACA,IAAI,CAACxB,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA;EACA,IAAIA,IAAI,CAACyB,MAAM,EAAE,OAAO,IAAI,EAAE;IAC5B,IAAIL,KAAK,GAAG5B,CAAC,KAAKyB,EAAE,GAAG,CAACZ,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGY,EAAE,CAAC,KAAMN,EAAE,GAAGM,EAAE;IAC5D,IAAII,MAAM,GAAG1B,CAAC,KAAKuB,EAAE,GAAG,CAACX,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGW,EAAE,CAAC,KAAML,EAAE,GAAGK,EAAE;IAC7D,IAAII,IAAI,GAAGzB,CAAC,KAAKsB,EAAE,GAAG,CAACV,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGU,EAAE,CAAC,KAAMJ,EAAE,GAAGI,EAAE;IAC3D,IAAI,EAAEpB,MAAM,GAAGC,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACuB,CAAC,GAAGD,IAAI,IAAI,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAGD,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IACnF,IAAI,CAACpB,IAAI,CAACyB,MAAM,EAAE;IAClB,IAAI1B,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxB,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxB,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxB,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxB,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxB,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIxB,MAAM,CAAEwB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,EAAErB,QAAQ,GAAGH,MAAM,EAAEyB,CAAC,GAAGD,CAAC;EAC7L;;EAEA;EACA,OAAOvB,IAAI,CAAC0B,IAAI,KAAKpC,CAAC,EAAE,IAAI,EAAEa,QAAQ,GAAGH,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACI,IAAI,CAAC,EAAE,OAAO,IAAI;EAC7E,IAAIA,IAAI,GAAGJ,IAAI,CAACI,IAAI,EAAE,OAAOJ,IAAI,CAACI,IAAI;;EAEtC;EACA,IAAID,QAAQ,EAAE,OAAQC,IAAI,GAAGD,QAAQ,CAACC,IAAI,GAAGA,IAAI,GAAG,OAAOD,QAAQ,CAACC,IAAI,EAAG,IAAI;;EAE/E;EACA,IAAI,CAACL,MAAM,EAAE,OAAO,IAAI,CAACE,KAAK,GAAGG,IAAI,EAAE,IAAI;;EAE3C;EACAA,IAAI,GAAGL,MAAM,CAACwB,CAAC,CAAC,GAAGnB,IAAI,GAAG,OAAOL,MAAM,CAACwB,CAAC,CAAC;;EAE1C;EACA,IAAI,CAACvB,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KACzGC,IAAI,MAAMD,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAAC,IAC/G,CAACC,IAAI,CAACyB,MAAM,EAAE;IACnB,IAAIvB,QAAQ,EAAEA,QAAQ,CAACsB,CAAC,CAAC,GAAGxB,IAAI,CAAC,KAC5B,IAAI,CAACC,KAAK,GAAGD,IAAI;EACxB;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAAS2B,SAASA,CAACD,IAAI,EAAE;EAC9B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGF,IAAI,CAACD,MAAM,EAAEF,CAAC,GAAGK,CAAC,EAAE,EAAEL,CAAC,EAAE,IAAI,CAACM,MAAM,CAACH,IAAI,CAACH,CAAC,CAAC,CAAC;EACjE,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
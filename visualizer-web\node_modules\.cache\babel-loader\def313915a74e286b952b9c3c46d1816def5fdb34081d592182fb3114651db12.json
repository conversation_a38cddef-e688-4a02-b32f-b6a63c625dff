{"ast": null, "code": "function safeRequestAnimationFrame(callback) {\n  if (typeof requestAnimationFrame !== 'undefined') requestAnimationFrame(callback);\n}\nexport default function setRafTimeout(callback) {\n  var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currTime = -1;\n  var shouldUpdate = function shouldUpdate(now) {\n    if (currTime < 0) {\n      currTime = now;\n    }\n    if (now - currTime > timeout) {\n      callback(now);\n      currTime = -1;\n    } else {\n      safeRequestAnimationFrame(shouldUpdate);\n    }\n  };\n  requestAnimationFrame(shouldUpdate);\n}", "map": {"version": 3, "names": ["safeRequestAnimationFrame", "callback", "requestAnimationFrame", "setRafTimeout", "timeout", "arguments", "length", "undefined", "currTime", "shouldUpdate", "now"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/react-smooth/es6/setRafTimeout.js"], "sourcesContent": ["function safeRequestAnimationFrame(callback) {\n  if (typeof requestAnimationFrame !== 'undefined') requestAnimationFrame(callback);\n}\nexport default function setRafTimeout(callback) {\n  var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currTime = -1;\n  var shouldUpdate = function shouldUpdate(now) {\n    if (currTime < 0) {\n      currTime = now;\n    }\n    if (now - currTime > timeout) {\n      callback(now);\n      currTime = -1;\n    } else {\n      safeRequestAnimationFrame(shouldUpdate);\n    }\n  };\n  requestAnimationFrame(shouldUpdate);\n}"], "mappings": "AAAA,SAASA,yBAAyBA,CAACC,QAAQ,EAAE;EAC3C,IAAI,OAAOC,qBAAqB,KAAK,WAAW,EAAEA,qBAAqB,CAACD,QAAQ,CAAC;AACnF;AACA,eAAe,SAASE,aAAaA,CAACF,QAAQ,EAAE;EAC9C,IAAIG,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACnF,IAAIG,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAE;IAC5C,IAAIF,QAAQ,GAAG,CAAC,EAAE;MAChBA,QAAQ,GAAGE,GAAG;IAChB;IACA,IAAIA,GAAG,GAAGF,QAAQ,GAAGJ,OAAO,EAAE;MAC5BH,QAAQ,CAACS,GAAG,CAAC;MACbF,QAAQ,GAAG,CAAC,CAAC;IACf,CAAC,MAAM;MACLR,yBAAyB,CAACS,YAAY,CAAC;IACzC;EACF,CAAC;EACDP,qBAAqB,CAACO,YAAY,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Selection } from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\nfunction arrayAll(select) {\n  return function () {\n    return array(select.apply(this, arguments));\n  };\n}\nexport default function (select) {\n  if (typeof select === \"function\") select = arrayAll(select);else select = selectorAll(select);\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n  return new Selection(subgroups, parents);\n}", "map": {"version": 3, "names": ["Selection", "array", "selectorAll", "arrayAll", "select", "apply", "arguments", "groups", "_groups", "m", "length", "subgroups", "parents", "j", "group", "n", "node", "i", "push", "call", "__data__"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selection/selectAll.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    return array(select.apply(this, arguments));\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,YAAY;AACpC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,mBAAmB;AAE3C,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,OAAO,YAAW;IAChB,OAAOH,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EAC7C,CAAC;AACH;AAEA,eAAe,UAASF,MAAM,EAAE;EAC9B,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGD,QAAQ,CAACC,MAAM,CAAC,CAAC,KACvDA,MAAM,GAAGF,WAAW,CAACE,MAAM,CAAC;EAEjC,KAAK,IAAIG,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,SAAS,GAAG,EAAE,EAAEC,OAAO,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAClG,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACrE,IAAID,IAAI,GAAGF,KAAK,CAACG,CAAC,CAAC,EAAE;QACnBN,SAAS,CAACO,IAAI,CAACd,MAAM,CAACe,IAAI,CAACH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAEH,CAAC,EAAEH,KAAK,CAAC,CAAC;QAC1DF,OAAO,CAACM,IAAI,CAACF,IAAI,CAAC;MACpB;IACF;EACF;EAEA,OAAO,IAAIhB,SAAS,CAACW,SAAS,EAAEC,OAAO,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
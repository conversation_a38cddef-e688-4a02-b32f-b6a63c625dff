{"ast": null, "code": "export default function constant(x) {\n  return () => x;\n}", "map": {"version": 3, "names": ["constant", "x"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-array/src/constant.js"], "sourcesContent": ["export default function constant(x) {\n  return () => x;\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,CAAC,EAAE;EAClC,OAAO,MAAMA,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
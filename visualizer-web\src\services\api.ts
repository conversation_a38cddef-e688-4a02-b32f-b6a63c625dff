import axios from 'axios';

// API Base URL - will use proxy in development
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? process.env.REACT_APP_API_URL || 'http://localhost:1231'
  : '';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API Types
export interface OverviewStats {
  total_dna_matches: number;
  total_individuals: number;
  total_trees: number;
  total_shared_segments: number;
  total_submitters: number;
  total_families: number;
  total_surnames: number;
  last_updated: string;
}

export interface MatchStats {
  total_matches: number;
  avg_shared_cm: number;
  max_shared_cm: number;
  min_shared_cm: number;
  matches_by_confidence: Record<string, number>;
  matches_by_relationship: Record<string, number>;
  cm_distribution: Array<{
    min_cm: number;
    max_cm: number;
    count: number;
  }>;
}

export interface CountryStats {
  country: string;
  count: number;
}

export interface IndividualStats {
  total_individuals: number;
  individuals_with_trees: number;
  gender_distribution: Record<string, number>;
  age_group_distribution: Record<string, number>;
}

export interface NetworkNode {
  id: string;
  label: string;
  type: string;
  // Optional fields for visualization
  name?: string;
  group?: string;
  size?: number;
  color?: string;
}

export interface NetworkEdge {
  source: string;
  target: string;
  weight: number;
  relationship: string;
  // Optional field for visualization
  value?: number;
}

export interface NetworkGraph {
  nodes: NetworkNode[];
  edges: NetworkEdge[];
  stats: {
    node_count: number;
    edge_count: number;
    min_cm: number;
    depth: number;
  };
}

export interface Clique {
  id: number;
  members: string[];
  size: number;
  avg_cm: number;
  total_connections: number;
}

export interface CliquesResponse {
  cliques: Clique[];
  stats: {
    total_cliques: number;
    avg_size: number;
    max_size: number;
    min_size: number;
  };
}

// API Functions
export const analyticsApi = {
  // Overview
  getOverview: (): Promise<OverviewStats> =>
    api.get('/api/v1/analytics/overview').then(res => res.data),

  // Match Statistics
  getMatchStats: (): Promise<MatchStats> =>
    api.get('/api/v1/analytics/matches/stats').then(res => res.data),

  // Geography
  getTopCountries: (limit = 10): Promise<CountryStats[]> =>
    api.get(`/api/v1/analytics/geography/top-countries?limit=${limit}`).then(res => res.data.countries),

  // Individual Statistics
  getIndividualStats: (): Promise<IndividualStats> =>
    api.get('/api/v1/analytics/individuals/stats').then(res => res.data),

  // Network Graph
  getNetworkGraph: (params: {
    depth?: number;
    min_cm?: number;
    limit?: number;
  } = {}): Promise<NetworkGraph> => {
    const queryParams = new URLSearchParams();
    if (params.depth) queryParams.append('depth', params.depth.toString());
    if (params.min_cm) queryParams.append('min_cm', params.min_cm.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    return api.get(`/api/v1/analytics/network/graph?${queryParams}`).then(res => res.data);
  },

  // Network Cliques
  getNetworkCliques: (params: {
    min_size?: number;
    limit?: number;
  } = {}): Promise<CliquesResponse> => {
    const queryParams = new URLSearchParams();
    if (params.min_size) queryParams.append('min_size', params.min_size.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    return api.get(`/api/v1/analytics/network/cliques?${queryParams}`).then(res => res.data);
  },

  // Data endpoints with filtering
  getMatches: (params: {
    limit?: number;
    offset?: number;
    min_cm?: number;
    max_cm?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.min_cm) queryParams.append('min_cm', params.min_cm.toString());
    if (params.max_cm) queryParams.append('max_cm', params.max_cm.toString());

    return api.get(`/api/v1/analytics/matches?${queryParams}`).then(res => res.data);
  },

  getIndividuals: (params: {
    limit?: number;
    offset?: number;
    tree_id?: number;
    country?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.tree_id) queryParams.append('tree_id', params.tree_id.toString());
    if (params.country) queryParams.append('country', params.country);

    return api.get(`/api/v1/analytics/individuals?${queryParams}`).then(res => res.data);
  },

  getSegments: (params: {
    limit?: number;
    offset?: number;
    chromosome?: number;
    min_length?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.chromosome) queryParams.append('chromosome', params.chromosome.toString());
    if (params.min_length) queryParams.append('min_length', params.min_length.toString());

    return api.get(`/api/v1/analytics/segments?${queryParams}`).then(res => res.data);
  },
};

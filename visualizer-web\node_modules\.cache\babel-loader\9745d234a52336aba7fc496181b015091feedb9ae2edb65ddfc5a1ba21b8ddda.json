{"ast": null, "code": "import { Timer, now } from \"./timer.js\";\nexport default function (callback, delay, time) {\n  var t = new Timer(),\n    total = delay;\n  if (delay == null) return t.restart(callback, delay, time), t;\n  t._restart = t.restart;\n  t.restart = function (callback, delay, time) {\n    delay = +delay, time = time == null ? now() : +time;\n    t._restart(function tick(elapsed) {\n      elapsed += total;\n      t._restart(tick, total += delay, time);\n      callback(elapsed);\n    }, delay, time);\n  };\n  t.restart(callback, delay, time);\n  return t;\n}", "map": {"version": 3, "names": ["Timer", "now", "callback", "delay", "time", "t", "total", "restart", "_restart", "tick", "elapsed"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-timer/src/interval.js"], "sourcesContent": ["import {Timer, now} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer, total = delay;\n  if (delay == null) return t.restart(callback, delay, time), t;\n  t._restart = t.restart;\n  t.restart = function(callback, delay, time) {\n    delay = +delay, time = time == null ? now() : +time;\n    t._restart(function tick(elapsed) {\n      elapsed += total;\n      t._restart(tick, total += delay, time);\n      callback(elapsed);\n    }, delay, time);\n  }\n  t.restart(callback, delay, time);\n  return t;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,GAAG,QAAO,YAAY;AAErC,eAAe,UAASC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,IAAIC,CAAC,GAAG,IAAIL,KAAK,CAAD,CAAC;IAAEM,KAAK,GAAGH,KAAK;EAChC,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAOE,CAAC,CAACE,OAAO,CAACL,QAAQ,EAAEC,KAAK,EAAEC,IAAI,CAAC,EAAEC,CAAC;EAC7DA,CAAC,CAACG,QAAQ,GAAGH,CAAC,CAACE,OAAO;EACtBF,CAAC,CAACE,OAAO,GAAG,UAASL,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE;IAC1CD,KAAK,GAAG,CAACA,KAAK,EAAEC,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAGH,GAAG,CAAC,CAAC,GAAG,CAACG,IAAI;IACnDC,CAAC,CAACG,QAAQ,CAAC,SAASC,IAAIA,CAACC,OAAO,EAAE;MAChCA,OAAO,IAAIJ,KAAK;MAChBD,CAAC,CAACG,QAAQ,CAACC,IAAI,EAAEH,KAAK,IAAIH,KAAK,EAAEC,IAAI,CAAC;MACtCF,QAAQ,CAACQ,OAAO,CAAC;IACnB,CAAC,EAAEP,KAAK,EAAEC,IAAI,CAAC;EACjB,CAAC;EACDC,CAAC,CAACE,OAAO,CAACL,QAAQ,EAAEC,KAAK,EAAEC,IAAI,CAAC;EAChC,OAAOC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
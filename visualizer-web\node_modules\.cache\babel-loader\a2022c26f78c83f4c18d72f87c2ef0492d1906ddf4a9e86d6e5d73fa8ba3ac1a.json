{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"f0f0f0bdbdbd636363\", \"f7f7f7cccccc969696525252\", \"f7f7f7cccccc969696636363252525\", \"f7f7f7d9d9d9bdbdbd969696636363252525\", \"f7f7f7d9d9d9bdbdbd969696737373525252252525\", \"fffffff0f0f0d9d9d9bdbdbd969696737373525252252525\", \"fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-single/Greys.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"f0f0f0bdbdbd636363\",\n  \"f7f7f7cccccc969696525252\",\n  \"f7f7f7cccccc969696636363252525\",\n  \"f7f7f7d9d9d9bdbdbd969696636363252525\",\n  \"f7f7f7d9d9d9bdbdbd969696737373525252252525\",\n  \"fffffff0f0f0d9d9d9bdbdbd969696737373525252252525\",\n  \"fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
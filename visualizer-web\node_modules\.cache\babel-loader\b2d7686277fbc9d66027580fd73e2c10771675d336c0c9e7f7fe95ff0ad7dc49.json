{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"e0ecf49ebcda8856a7\", \"edf8fbb3cde38c96c688419d\", \"edf8fbb3cde38c96c68856a7810f7c\", \"edf8fbbfd3e69ebcda8c96c68856a7810f7c\", \"edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b\", \"f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b\", \"f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-multi/BuPu.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e0ecf49ebcda8856a7\",\n  \"edf8fbb3cde38c96c688419d\",\n  \"edf8fbb3cde38c96c68856a7810f7c\",\n  \"edf8fbbfd3e69ebcda8c96c68856a7810f7c\",\n  \"edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b\",\n  \"f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b\",\n  \"f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
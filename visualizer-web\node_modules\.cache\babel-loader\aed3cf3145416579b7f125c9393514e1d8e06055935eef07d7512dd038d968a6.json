{"ast": null, "code": "export default function number(x) {\n  return x === null ? NaN : +x;\n}\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["number", "x", "NaN", "numbers", "values", "valueof", "undefined", "value", "index"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-array/src/number.js"], "sourcesContent": ["export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,CAAC,EAAE;EAChC,OAAOA,CAAC,KAAK,IAAI,GAAGC,GAAG,GAAG,CAACD,CAAC;AAC9B;AAEA,OAAO,UAAUE,OAAOA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxC,IAAIA,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAIG,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9C,MAAMA,KAAK;MACb;IACF;EACF,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAI,CAACG,KAAK,GAAGF,OAAO,CAACE,KAAK,EAAE,EAAEC,KAAK,EAAEJ,MAAM,CAAC,KAAK,IAAI,IAAI,CAACG,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClF,MAAMA,KAAK;MACb;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
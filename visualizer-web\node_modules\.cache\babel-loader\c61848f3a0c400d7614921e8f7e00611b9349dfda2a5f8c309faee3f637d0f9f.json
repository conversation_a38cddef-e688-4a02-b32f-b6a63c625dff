{"ast": null, "code": "import Half from \"./half.js\";\nexport default function (x, radius) {\n  var data,\n    x0 = this._x0,\n    x1,\n    x2,\n    x3 = this._x1,\n    halves = [],\n    node = this._root,\n    q,\n    i;\n  if (node) halves.push(new Half(node, x0, x3));\n  if (radius == null) radius = Infinity;else {\n    x0 = x - radius;\n    x3 = x + radius;\n  }\n  while (q = halves.pop()) {\n    // Stop searching if this half can’t contain a closer node.\n    if (!(node = q.node) || (x1 = q.x0) > x3 || (x2 = q.x1) < x0) continue;\n\n    // Bisect the current half.\n    if (node.length) {\n      var xm = (x1 + x2) / 2;\n      halves.push(new Half(node[1], xm, x2), new Half(node[0], x1, xm));\n\n      // Visit the closest half first.\n      if (i = +(x >= xm)) {\n        q = halves[halves.length - 1];\n        halves[halves.length - 1] = halves[halves.length - 1 - i];\n        halves[halves.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var d = Math.abs(x - +this._x.call(null, node.data));\n      if (d < radius) {\n        radius = d;\n        x0 = x - d;\n        x3 = x + d;\n        data = node.data;\n      }\n    }\n  }\n  return data;\n}", "map": {"version": 3, "names": ["Half", "x", "radius", "data", "x0", "_x0", "x1", "x2", "x3", "_x1", "halves", "node", "_root", "q", "i", "push", "Infinity", "pop", "length", "xm", "d", "Math", "abs", "_x", "call"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-binarytree/src/find.js"], "sourcesContent": ["import Half from \"./half.js\";\n\nexport default function(x, radius) {\n  var data,\n      x0 = this._x0,\n      x1,\n      x2,\n      x3 = this._x1,\n      halves = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) halves.push(new Half(node, x0, x3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius;\n    x3 = x + radius;\n  }\n\n  while (q = halves.pop()) {\n\n    // Stop searching if this half can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (x2 = q.x1) < x0) continue;\n\n    // Bisect the current half.\n    if (node.length) {\n      var xm = (x1 + x2) / 2;\n\n      halves.push(\n        new Half(node[1], xm, x2),\n        new Half(node[0], x1, xm)\n      );\n\n      // Visit the closest half first.\n      if (i = +(x >= xm)) {\n        q = halves[halves.length - 1];\n        halves[halves.length - 1] = halves[halves.length - 1 - i];\n        halves[halves.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var d = Math.abs(x - +this._x.call(null, node.data));\n      if (d < radius) {\n        radius = d;\n        x0 = x - d;\n        x3 = x + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,CAAC,EAAEC,MAAM,EAAE;EACjC,IAAIC,IAAI;IACJC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE;IACFC,EAAE;IACFC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,MAAM,GAAG,EAAE;IACXC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,CAAC;IACDC,CAAC;EAEL,IAAIH,IAAI,EAAED,MAAM,CAACK,IAAI,CAAC,IAAIf,IAAI,CAACW,IAAI,EAAEP,EAAE,EAAEI,EAAE,CAAC,CAAC;EAC7C,IAAIN,MAAM,IAAI,IAAI,EAAEA,MAAM,GAAGc,QAAQ,CAAC,KACjC;IACHZ,EAAE,GAAGH,CAAC,GAAGC,MAAM;IACfM,EAAE,GAAGP,CAAC,GAAGC,MAAM;EACjB;EAEA,OAAOW,CAAC,GAAGH,MAAM,CAACO,GAAG,CAAC,CAAC,EAAE;IAEvB;IACA,IAAI,EAAEN,IAAI,GAAGE,CAAC,CAACF,IAAI,CAAC,IACb,CAACL,EAAE,GAAGO,CAAC,CAACT,EAAE,IAAII,EAAE,IAChB,CAACD,EAAE,GAAGM,CAAC,CAACP,EAAE,IAAIF,EAAE,EAAE;;IAEzB;IACA,IAAIO,IAAI,CAACO,MAAM,EAAE;MACf,IAAIC,EAAE,GAAG,CAACb,EAAE,GAAGC,EAAE,IAAI,CAAC;MAEtBG,MAAM,CAACK,IAAI,CACT,IAAIf,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEZ,EAAE,CAAC,EACzB,IAAIP,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,EAAEL,EAAE,EAAEa,EAAE,CAC1B,CAAC;;MAED;MACA,IAAIL,CAAC,GAAG,EAAEb,CAAC,IAAIkB,EAAE,CAAC,EAAE;QAClBN,CAAC,GAAGH,MAAM,CAACA,MAAM,CAACQ,MAAM,GAAG,CAAC,CAAC;QAC7BR,MAAM,CAACA,MAAM,CAACQ,MAAM,GAAG,CAAC,CAAC,GAAGR,MAAM,CAACA,MAAM,CAACQ,MAAM,GAAG,CAAC,GAAGJ,CAAC,CAAC;QACzDJ,MAAM,CAACA,MAAM,CAACQ,MAAM,GAAG,CAAC,GAAGJ,CAAC,CAAC,GAAGD,CAAC;MACnC;IACF;;IAEA;IAAA,KACK;MACH,IAAIO,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACrB,CAAC,GAAG,CAAC,IAAI,CAACsB,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEb,IAAI,CAACR,IAAI,CAAC,CAAC;MACpD,IAAIiB,CAAC,GAAGlB,MAAM,EAAE;QACdA,MAAM,GAAGkB,CAAC;QACVhB,EAAE,GAAGH,CAAC,GAAGmB,CAAC;QACVZ,EAAE,GAAGP,CAAC,GAAGmB,CAAC;QACVjB,IAAI,GAAGQ,IAAI,CAACR,IAAI;MAClB;IACF;EACF;EAEA,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
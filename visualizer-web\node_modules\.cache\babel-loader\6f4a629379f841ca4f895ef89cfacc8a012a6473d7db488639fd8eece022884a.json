{"ast": null, "code": "module.exports = generateBoundsFunction;\nmodule.exports.generateFunctionBody = generateBoundsFunctionBody;\nconst createPatternBuilder = require('./createPatternBuilder');\nfunction generateBoundsFunction(dimension) {\n  let code = generateBoundsFunctionBody(dimension);\n  return new Function('bodies', 'settings', 'random', code);\n}\nfunction generateBoundsFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  var boundingBox = {\n    ${pattern('min_{var}: 0, max_{var}: 0,', {\n    indent: 4\n  })}\n  };\n\n  return {\n    box: boundingBox,\n\n    update: updateBoundingBox,\n\n    reset: resetBoundingBox,\n\n    getBestNewPosition: function (neighbors) {\n      var ${pattern('base_{var} = 0', {\n    join: ', '\n  })};\n\n      if (neighbors.length) {\n        for (var i = 0; i < neighbors.length; ++i) {\n          let neighborPos = neighbors[i].pos;\n          ${pattern('base_{var} += neighborPos.{var};', {\n    indent: 10\n  })}\n        }\n\n        ${pattern('base_{var} /= neighbors.length;', {\n    indent: 8\n  })}\n      } else {\n        ${pattern('base_{var} = (boundingBox.min_{var} + boundingBox.max_{var}) / 2;', {\n    indent: 8\n  })}\n      }\n\n      var springLength = settings.springLength;\n      return {\n        ${pattern('{var}: base_{var} + (random.nextDouble() - 0.5) * springLength,', {\n    indent: 8\n  })}\n      };\n    }\n  };\n\n  function updateBoundingBox() {\n    var i = bodies.length;\n    if (i === 0) return; // No bodies - no borders.\n\n    ${pattern('var max_{var} = -Infinity;', {\n    indent: 4\n  })}\n    ${pattern('var min_{var} = Infinity;', {\n    indent: 4\n  })}\n\n    while(i--) {\n      // this is O(n), it could be done faster with quadtree, if we check the root node bounds\n      var bodyPos = bodies[i].pos;\n      ${pattern('if (bodyPos.{var} < min_{var}) min_{var} = bodyPos.{var};', {\n    indent: 6\n  })}\n      ${pattern('if (bodyPos.{var} > max_{var}) max_{var} = bodyPos.{var};', {\n    indent: 6\n  })}\n    }\n\n    ${pattern('boundingBox.min_{var} = min_{var};', {\n    indent: 4\n  })}\n    ${pattern('boundingBox.max_{var} = max_{var};', {\n    indent: 4\n  })}\n  }\n\n  function resetBoundingBox() {\n    ${pattern('boundingBox.min_{var} = boundingBox.max_{var} = 0;', {\n    indent: 4\n  })}\n  }\n`;\n  return code;\n}", "map": {"version": 3, "names": ["module", "exports", "generateBoundsFunction", "generateFunctionBody", "generateBoundsFunctionBody", "createPatternBuilder", "require", "dimension", "code", "Function", "pattern", "indent", "join"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/generateBounds.js"], "sourcesContent": ["\nmodule.exports = generateBoundsFunction;\nmodule.exports.generateFunctionBody = generateBoundsFunctionBody;\n\nconst createPatternBuilder = require('./createPatternBuilder');\n\nfunction generateBoundsFunction(dimension) {\n  let code = generateBoundsFunctionBody(dimension);\n  return new Function('bodies', 'settings', 'random', code);\n}\n\nfunction generateBoundsFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n\n  let code = `\n  var boundingBox = {\n    ${pattern('min_{var}: 0, max_{var}: 0,', {indent: 4})}\n  };\n\n  return {\n    box: boundingBox,\n\n    update: updateBoundingBox,\n\n    reset: resetBoundingBox,\n\n    getBestNewPosition: function (neighbors) {\n      var ${pattern('base_{var} = 0', {join: ', '})};\n\n      if (neighbors.length) {\n        for (var i = 0; i < neighbors.length; ++i) {\n          let neighborPos = neighbors[i].pos;\n          ${pattern('base_{var} += neighborPos.{var};', {indent: 10})}\n        }\n\n        ${pattern('base_{var} /= neighbors.length;', {indent: 8})}\n      } else {\n        ${pattern('base_{var} = (boundingBox.min_{var} + boundingBox.max_{var}) / 2;', {indent: 8})}\n      }\n\n      var springLength = settings.springLength;\n      return {\n        ${pattern('{var}: base_{var} + (random.nextDouble() - 0.5) * springLength,', {indent: 8})}\n      };\n    }\n  };\n\n  function updateBoundingBox() {\n    var i = bodies.length;\n    if (i === 0) return; // No bodies - no borders.\n\n    ${pattern('var max_{var} = -Infinity;', {indent: 4})}\n    ${pattern('var min_{var} = Infinity;', {indent: 4})}\n\n    while(i--) {\n      // this is O(n), it could be done faster with quadtree, if we check the root node bounds\n      var bodyPos = bodies[i].pos;\n      ${pattern('if (bodyPos.{var} < min_{var}) min_{var} = bodyPos.{var};', {indent: 6})}\n      ${pattern('if (bodyPos.{var} > max_{var}) max_{var} = bodyPos.{var};', {indent: 6})}\n    }\n\n    ${pattern('boundingBox.min_{var} = min_{var};', {indent: 4})}\n    ${pattern('boundingBox.max_{var} = max_{var};', {indent: 4})}\n  }\n\n  function resetBoundingBox() {\n    ${pattern('boundingBox.min_{var} = boundingBox.max_{var} = 0;', {indent: 4})}\n  }\n`;\n  return code;\n}\n"], "mappings": "AACAA,MAAM,CAACC,OAAO,GAAGC,sBAAsB;AACvCF,MAAM,CAACC,OAAO,CAACE,oBAAoB,GAAGC,0BAA0B;AAEhE,MAAMC,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9D,SAASJ,sBAAsBA,CAACK,SAAS,EAAE;EACzC,IAAIC,IAAI,GAAGJ,0BAA0B,CAACG,SAAS,CAAC;EAChD,OAAO,IAAIE,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAED,IAAI,CAAC;AAC3D;AAEA,SAASJ,0BAA0BA,CAACG,SAAS,EAAE;EAC7C,IAAIG,OAAO,GAAGL,oBAAoB,CAACE,SAAS,CAAC;EAE7C,IAAIC,IAAI,GAAG;AACb;AACA,MAAME,OAAO,CAAC,6BAA6B,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYD,OAAO,CAAC,gBAAgB,EAAE;IAACE,IAAI,EAAE;EAAI,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA,YAAYF,OAAO,CAAC,kCAAkC,EAAE;IAACC,MAAM,EAAE;EAAE,CAAC,CAAC;AACrE;AACA;AACA,UAAUD,OAAO,CAAC,iCAAiC,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACjE;AACA,UAAUD,OAAO,CAAC,mEAAmE,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACnG;AACA;AACA;AACA;AACA,UAAUD,OAAO,CAAC,iEAAiE,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,OAAO,CAAC,4BAA4B,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACxD,MAAMD,OAAO,CAAC,2BAA2B,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA,QAAQD,OAAO,CAAC,2DAA2D,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACzF,QAAQD,OAAO,CAAC,2DAA2D,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACzF;AACA;AACA,MAAMD,OAAO,CAAC,oCAAoC,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAChE,MAAMD,OAAO,CAAC,oCAAoC,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAChE;AACA;AACA;AACA,MAAMD,OAAO,CAAC,oDAAoD,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAChF;AACA,CAAC;EACC,OAAOH,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useSlot;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _useForkRef = _interopRequireDefault(require("@mui/utils/useForkRef"));
var _appendOwnerState = _interopRequireDefault(require("@mui/utils/appendOwnerState"));
var _resolveComponentProps = _interopRequireDefault(require("@mui/utils/resolveComponentProps"));
var _mergeSlotProps2 = _interopRequireDefault(require("@mui/utils/mergeSlotProps"));
const _excluded = ["className", "elementType", "ownerState", "externalForwardedProps", "getSlotOwnerState", "internalForwardedProps"],
  _excluded2 = ["component", "slots", "slotProps"],
  _excluded3 = ["component"];
/**
 * An internal function to create a Material UI slot.
 *
 * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop
 * while Base UI does not need to support leaf component customization.
 *
 * @param {string} name: name of the slot
 * @param {object} parameters
 * @returns {[Slot, slotProps]} The slot's React component and the slot's props
 *
 * Note: the returned slot's props
 * - will never contain `component` prop.
 * - might contain `as` prop.
 */
function useSlot(
/**
 * The slot's name. All Material UI components should have `root` slot.
 *
 * If the name is `root`, the logic behaves differently from other slots,
 * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.
 */
name, parameters) {
  const {
      className,
      elementType: initialElementType,
      ownerState,
      externalForwardedProps,
      getSlotOwnerState,
      internalForwardedProps
    } = parameters,
    useSlotPropsParams = (0, _objectWithoutPropertiesLoose2.default)(parameters, _excluded);
  const {
      component: rootComponent,
      slots = {
        [name]: undefined
      },
      slotProps = {
        [name]: undefined
      }
    } = externalForwardedProps,
    other = (0, _objectWithoutPropertiesLoose2.default)(externalForwardedProps, _excluded2);
  const elementType = slots[name] || initialElementType;

  // `slotProps[name]` can be a callback that receives the component's ownerState.
  // `resolvedComponentsProps` is always a plain object.
  const resolvedComponentsProps = (0, _resolveComponentProps.default)(slotProps[name], ownerState);
  const _mergeSlotProps = (0, _mergeSlotProps2.default)((0, _extends2.default)({
      className
    }, useSlotPropsParams, {
      externalForwardedProps: name === 'root' ? other : undefined,
      externalSlotProps: resolvedComponentsProps
    })),
    {
      props: {
        component: slotComponent
      },
      internalRef
    } = _mergeSlotProps,
    mergedProps = (0, _objectWithoutPropertiesLoose2.default)(_mergeSlotProps.props, _excluded3);
  const ref = (0, _useForkRef.default)(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, parameters.ref);
  const slotOwnerState = getSlotOwnerState ? getSlotOwnerState(mergedProps) : {};
  const finalOwnerState = (0, _extends2.default)({}, ownerState, slotOwnerState);
  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;
  const props = (0, _appendOwnerState.default)(elementType, (0, _extends2.default)({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps, name !== 'root' && !slots[name] && internalForwardedProps, mergedProps, LeafComponent && {
    as: LeafComponent
  }, {
    ref
  }), finalOwnerState);
  Object.keys(slotOwnerState).forEach(propName => {
    delete props[propName];
  });
  return [elementType, props];
}
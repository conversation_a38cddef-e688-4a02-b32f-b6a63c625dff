{"ast": null, "code": "const createPatternBuilder = require('./createPatternBuilder');\nmodule.exports = generateCreateDragForceFunction;\nmodule.exports.generateCreateDragForceFunctionBody = generateCreateDragForceFunctionBody;\nfunction generateCreateDragForceFunction(dimension) {\n  let code = generateCreateDragForceFunctionBody(dimension);\n  return new Function('options', code);\n}\nfunction generateCreateDragForceFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  if (!Number.isFinite(options.dragCoefficient)) throw new Error('dragCoefficient is not a finite number');\n\n  return {\n    update: function(body) {\n      ${pattern('body.force.{var} -= options.dragCoefficient * body.velocity.{var};', {\n    indent: 6\n  })}\n    }\n  };\n`;\n  return code;\n}", "map": {"version": 3, "names": ["createPatternBuilder", "require", "module", "exports", "generateCreateDragForceFunction", "generateCreateDragForceFunctionBody", "dimension", "code", "Function", "pattern", "indent"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/generateCreateDragForce.js"], "sourcesContent": ["const createPatternBuilder = require('./createPatternBuilder');\n\nmodule.exports = generateCreateDragForceFunction;\nmodule.exports.generateCreateDragForceFunctionBody = generateCreateDragForceFunctionBody;\n\nfunction generateCreateDragForceFunction(dimension) {\n  let code = generateCreateDragForceFunctionBody(dimension);\n  return new Function('options', code);\n}\n\nfunction generateCreateDragForceFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  if (!Number.isFinite(options.dragCoefficient)) throw new Error('dragCoefficient is not a finite number');\n\n  return {\n    update: function(body) {\n      ${pattern('body.force.{var} -= options.dragCoefficient * body.velocity.{var};', {indent: 6})}\n    }\n  };\n`;\n  return code;\n}\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9DC,MAAM,CAACC,OAAO,GAAGC,+BAA+B;AAChDF,MAAM,CAACC,OAAO,CAACE,mCAAmC,GAAGA,mCAAmC;AAExF,SAASD,+BAA+BA,CAACE,SAAS,EAAE;EAClD,IAAIC,IAAI,GAAGF,mCAAmC,CAACC,SAAS,CAAC;EACzD,OAAO,IAAIE,QAAQ,CAAC,SAAS,EAAED,IAAI,CAAC;AACtC;AAEA,SAASF,mCAAmCA,CAACC,SAAS,EAAE;EACtD,IAAIG,OAAO,GAAGT,oBAAoB,CAACM,SAAS,CAAC;EAC7C,IAAIC,IAAI,GAAG;AACb;AACA;AACA;AACA;AACA,QAAQE,OAAO,CAAC,oEAAoE,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAClG;AACA;AACA,CAAC;EACC,OAAOH,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
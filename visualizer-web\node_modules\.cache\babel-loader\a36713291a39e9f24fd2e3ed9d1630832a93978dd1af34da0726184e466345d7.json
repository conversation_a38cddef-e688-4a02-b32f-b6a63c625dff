{"ast": null, "code": "import sparse from \"./sparse.js\";\nimport { Selection } from \"./index.js\";\nexport default function () {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function (child) {\n    return this._parent.insertBefore(child, this._next);\n  },\n  insertBefore: function (child, next) {\n    return this._parent.insertBefore(child, next);\n  },\n  querySelector: function (selector) {\n    return this._parent.querySelector(selector);\n  },\n  querySelectorAll: function (selector) {\n    return this._parent.querySelectorAll(selector);\n  }\n};", "map": {"version": 3, "names": ["sparse", "Selection", "_enter", "_groups", "map", "_parents", "EnterNode", "parent", "datum", "ownerDocument", "namespaceURI", "_next", "_parent", "__data__", "prototype", "constructor", "append<PERSON><PERSON><PERSON>", "child", "insertBefore", "next", "querySelector", "selector", "querySelectorAll"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selection/enter.js"], "sourcesContent": ["import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,SAAQC,SAAS,QAAO,YAAY;AAEpC,eAAe,YAAW;EACxB,OAAO,IAAIA,SAAS,CAAC,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC,EAAE,IAAI,CAACK,QAAQ,CAAC;AAC9E;AAEA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACvC,IAAI,CAACC,aAAa,GAAGF,MAAM,CAACE,aAAa;EACzC,IAAI,CAACC,YAAY,GAAGH,MAAM,CAACG,YAAY;EACvC,IAAI,CAACC,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,OAAO,GAAGL,MAAM;EACrB,IAAI,CAACM,QAAQ,GAAGL,KAAK;AACvB;AAEAF,SAAS,CAACQ,SAAS,GAAG;EACpBC,WAAW,EAAET,SAAS;EACtBU,WAAW,EAAE,SAAAA,CAASC,KAAK,EAAE;IAAE,OAAO,IAAI,CAACL,OAAO,CAACM,YAAY,CAACD,KAAK,EAAE,IAAI,CAACN,KAAK,CAAC;EAAE,CAAC;EACrFO,YAAY,EAAE,SAAAA,CAASD,KAAK,EAAEE,IAAI,EAAE;IAAE,OAAO,IAAI,CAACP,OAAO,CAACM,YAAY,CAACD,KAAK,EAAEE,IAAI,CAAC;EAAE,CAAC;EACtFC,aAAa,EAAE,SAAAA,CAASC,QAAQ,EAAE;IAAE,OAAO,IAAI,CAACT,OAAO,CAACQ,aAAa,CAACC,QAAQ,CAAC;EAAE,CAAC;EAClFC,gBAAgB,EAAE,SAAAA,CAASD,QAAQ,EAAE;IAAE,OAAO,IAAI,CAACT,OAAO,CAACU,gBAAgB,CAACD,QAAQ,CAAC;EAAE;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
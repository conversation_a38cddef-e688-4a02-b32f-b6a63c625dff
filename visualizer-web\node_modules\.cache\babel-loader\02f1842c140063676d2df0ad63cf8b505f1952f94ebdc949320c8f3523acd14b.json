{"ast": null, "code": "export default function (node, x0, y0, x1, y1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.x1 = x1;\n  this.y1 = y1;\n}", "map": {"version": 3, "names": ["node", "x0", "y0", "x1", "y1"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-quadtree/src/quad.js"], "sourcesContent": ["export default function(node, x0, y0, x1, y1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.x1 = x1;\n  this.y1 = y1;\n}\n"], "mappings": "AAAA,eAAe,UAASA,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC5C,IAAI,CAACJ,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
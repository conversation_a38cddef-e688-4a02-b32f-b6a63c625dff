{"ast": null, "code": "const createPatternBuilder = require('./createPatternBuilder');\nmodule.exports = generateIntegratorFunction;\nmodule.exports.generateIntegratorFunctionBody = generateIntegratorFunctionBody;\nfunction generateIntegratorFunction(dimension) {\n  let code = generateIntegratorFunctionBody(dimension);\n  return new Function('bodies', 'timeStep', 'adaptiveTimeStepWeight', code);\n}\nfunction generateIntegratorFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  var length = bodies.length;\n  if (length === 0) return 0;\n\n  ${pattern('var d{var} = 0, t{var} = 0;', {\n    indent: 2\n  })}\n\n  for (var i = 0; i < length; ++i) {\n    var body = bodies[i];\n    if (body.isPinned) continue;\n\n    if (adaptiveTimeStepWeight && body.springCount) {\n      timeStep = (adaptiveTimeStepWeight * body.springLength/body.springCount);\n    }\n\n    var coeff = timeStep / body.mass;\n\n    ${pattern('body.velocity.{var} += coeff * body.force.{var};', {\n    indent: 4\n  })}\n    ${pattern('var v{var} = body.velocity.{var};', {\n    indent: 4\n  })}\n    var v = Math.sqrt(${pattern('v{var} * v{var}', {\n    join: ' + '\n  })});\n\n    if (v > 1) {\n      // We normalize it so that we move within timeStep range. \n      // for the case when v <= 1 - we let velocity to fade out.\n      ${pattern('body.velocity.{var} = v{var} / v;', {\n    indent: 6\n  })}\n    }\n\n    ${pattern('d{var} = timeStep * body.velocity.{var};', {\n    indent: 4\n  })}\n\n    ${pattern('body.pos.{var} += d{var};', {\n    indent: 4\n  })}\n\n    ${pattern('t{var} += Math.abs(d{var});', {\n    indent: 4\n  })}\n  }\n\n  return (${pattern('t{var} * t{var}', {\n    join: ' + '\n  })})/length;\n`;\n  return code;\n}", "map": {"version": 3, "names": ["createPatternBuilder", "require", "module", "exports", "generateIntegratorFunction", "generateIntegratorFunctionBody", "dimension", "code", "Function", "pattern", "indent", "join"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/generateIntegrator.js"], "sourcesContent": ["const createPatternBuilder = require('./createPatternBuilder');\n\nmodule.exports = generateIntegratorFunction;\nmodule.exports.generateIntegratorFunctionBody = generateIntegratorFunctionBody;\n\nfunction generateIntegratorFunction(dimension) {\n  let code = generateIntegratorFunctionBody(dimension);\n  return new Function('bodies', 'timeStep', 'adaptiveTimeStepWeight', code);\n}\n\nfunction generateIntegratorFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  var length = bodies.length;\n  if (length === 0) return 0;\n\n  ${pattern('var d{var} = 0, t{var} = 0;', {indent: 2})}\n\n  for (var i = 0; i < length; ++i) {\n    var body = bodies[i];\n    if (body.isPinned) continue;\n\n    if (adaptiveTimeStepWeight && body.springCount) {\n      timeStep = (adaptiveTimeStepWeight * body.springLength/body.springCount);\n    }\n\n    var coeff = timeStep / body.mass;\n\n    ${pattern('body.velocity.{var} += coeff * body.force.{var};', {indent: 4})}\n    ${pattern('var v{var} = body.velocity.{var};', {indent: 4})}\n    var v = Math.sqrt(${pattern('v{var} * v{var}', {join: ' + '})});\n\n    if (v > 1) {\n      // We normalize it so that we move within timeStep range. \n      // for the case when v <= 1 - we let velocity to fade out.\n      ${pattern('body.velocity.{var} = v{var} / v;', {indent: 6})}\n    }\n\n    ${pattern('d{var} = timeStep * body.velocity.{var};', {indent: 4})}\n\n    ${pattern('body.pos.{var} += d{var};', {indent: 4})}\n\n    ${pattern('t{var} += Math.abs(d{var});', {indent: 4})}\n  }\n\n  return (${pattern('t{var} * t{var}', {join: ' + '})})/length;\n`;\n  return code;\n}\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9DC,MAAM,CAACC,OAAO,GAAGC,0BAA0B;AAC3CF,MAAM,CAACC,OAAO,CAACE,8BAA8B,GAAGA,8BAA8B;AAE9E,SAASD,0BAA0BA,CAACE,SAAS,EAAE;EAC7C,IAAIC,IAAI,GAAGF,8BAA8B,CAACC,SAAS,CAAC;EACpD,OAAO,IAAIE,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE,wBAAwB,EAAED,IAAI,CAAC;AAC3E;AAEA,SAASF,8BAA8BA,CAACC,SAAS,EAAE;EACjD,IAAIG,OAAO,GAAGT,oBAAoB,CAACM,SAAS,CAAC;EAC7C,IAAIC,IAAI,GAAG;AACb;AACA;AACA;AACA,IAAIE,OAAO,CAAC,6BAA6B,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,OAAO,CAAC,kDAAkD,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAC9E,MAAMD,OAAO,CAAC,mCAAmC,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/D,wBAAwBD,OAAO,CAAC,iBAAiB,EAAE;IAACE,IAAI,EAAE;EAAK,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA,QAAQF,OAAO,CAAC,mCAAmC,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACjE;AACA;AACA,MAAMD,OAAO,CAAC,0CAA0C,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACtE;AACA,MAAMD,OAAO,CAAC,2BAA2B,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACvD;AACA,MAAMD,OAAO,CAAC,6BAA6B,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACzD;AACA;AACA,YAAYD,OAAO,CAAC,iBAAiB,EAAE;IAACE,IAAI,EAAE;EAAK,CAAC,CAAC;AACrD,CAAC;EACC,OAAOJ,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
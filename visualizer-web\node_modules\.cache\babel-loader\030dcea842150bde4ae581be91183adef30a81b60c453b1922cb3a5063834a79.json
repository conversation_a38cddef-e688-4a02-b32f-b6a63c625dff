{"ast": null, "code": "import { Selection, root } from \"./selection/index.js\";\nexport default function (selector) {\n  return typeof selector === \"string\" ? new Selection([[document.querySelector(selector)]], [document.documentElement]) : new Selection([[selector]], root);\n}", "map": {"version": 3, "names": ["Selection", "root", "selector", "document", "querySelector", "documentElement"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/select.js"], "sourcesContent": ["import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,EAAEC,IAAI,QAAO,sBAAsB;AAEpD,eAAe,UAASC,QAAQ,EAAE;EAChC,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAC7B,IAAIF,SAAS,CAAC,CAAC,CAACG,QAAQ,CAACC,aAAa,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACC,QAAQ,CAACE,eAAe,CAAC,CAAC,GAC/E,IAAIL,SAAS,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAED,IAAI,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
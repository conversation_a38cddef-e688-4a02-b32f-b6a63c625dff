{"ast": null, "code": "module.exports = random;\nmodule.exports.random = random, module.exports.randomIterator = randomIterator;\n\n/**\n * Creates seeded PRNG with two methods:\n *   next() and nextDouble()\n */\nfunction random(inputSeed) {\n  var seed = typeof inputSeed === 'number' ? inputSeed : +new Date();\n  return new Generator(seed);\n}\nfunction Generator(seed) {\n  this.seed = seed;\n}\n\n/**\n  * Generates random integer number in the range from 0 (inclusive) to maxValue (exclusive)\n  *\n  * @param maxValue Number REQUIRED. Omitting this number will result in NaN values from PRNG.\n  */\nGenerator.prototype.next = next;\n\n/**\n  * Generates random double number in the range from 0 (inclusive) to 1 (exclusive)\n  * This function is the same as Math.random() (except that it could be seeded)\n  */\nGenerator.prototype.nextDouble = nextDouble;\n\n/**\n * Returns a random real number from uniform distribution in [0, 1)\n */\nGenerator.prototype.uniform = nextDouble;\n\n/**\n * Returns a random real number from a Gaussian distribution\n * with 0 as a mean, and 1 as standard deviation u ~ N(0,1)\n */\nGenerator.prototype.gaussian = gaussian;\n\n/**\n * Returns a floating-point, pseudo-random number that's greater than \n * or equal to 0 and less than 1, with approximately uniform distribution over that range\n * \n * Note: This method is the same as nextDouble(), but is here for\n * compatibility with similar Math.random()\n */\nGenerator.prototype.random = nextDouble;\nfunction gaussian() {\n  // use the polar form of the Box-Muller transform\n  // based on https://introcs.cs.princeton.edu/java/23recursion/StdRandom.java\n  var r, x, y;\n  do {\n    x = this.nextDouble() * 2 - 1;\n    y = this.nextDouble() * 2 - 1;\n    r = x * x + y * y;\n  } while (r >= 1 || r === 0);\n  return x * Math.sqrt(-2 * Math.log(r) / r);\n}\n\n/**\n * See https://twitter.com/anvaka/status/1296182534150135808\n */\nGenerator.prototype.levy = levy;\nfunction levy() {\n  var beta = 3 / 2;\n  var sigma = Math.pow(gamma(1 + beta) * Math.sin(Math.PI * beta / 2) / (gamma((1 + beta) / 2) * beta * Math.pow(2, (beta - 1) / 2)), 1 / beta);\n  return this.gaussian() * sigma / Math.pow(Math.abs(this.gaussian()), 1 / beta);\n}\n\n// gamma function approximation\nfunction gamma(z) {\n  return Math.sqrt(2 * Math.PI / z) * Math.pow(1 / Math.E * (z + 1 / (12 * z - 1 / (10 * z))), z);\n}\nfunction nextDouble() {\n  var seed = this.seed;\n  // Robert Jenkins' 32 bit integer hash function.\n  seed = seed + 0x7ed55d16 + (seed << 12) & 0xffffffff;\n  seed = (seed ^ 0xc761c23c ^ seed >>> 19) & 0xffffffff;\n  seed = seed + 0x165667b1 + (seed << 5) & 0xffffffff;\n  seed = (seed + 0xd3a2646c ^ seed << 9) & 0xffffffff;\n  seed = seed + 0xfd7046c5 + (seed << 3) & 0xffffffff;\n  seed = (seed ^ 0xb55a4f09 ^ seed >>> 16) & 0xffffffff;\n  this.seed = seed;\n  return (seed & 0xfffffff) / 0x10000000;\n}\nfunction next(maxValue) {\n  return Math.floor(this.nextDouble() * maxValue);\n}\n\n/*\n * Creates iterator over array, which returns items of array in random order\n * Time complexity is guaranteed to be O(n);\n */\nfunction randomIterator(array, customRandom) {\n  var localRandom = customRandom || random();\n  if (typeof localRandom.next !== 'function') {\n    throw new Error('customRandom does not match expected API: next() function is missing');\n  }\n  return {\n    /**\n     * Visits every single element of a collection once, in a random order.\n     * Note: collection is modified in place.\n     */\n    forEach: forEach,\n    /**\n     * Shuffles array randomly, in place.\n     */\n    shuffle: shuffle\n  };\n  function shuffle() {\n    var i, j, t;\n    for (i = array.length - 1; i > 0; --i) {\n      j = localRandom.next(i + 1); // i inclusive\n      t = array[j];\n      array[j] = array[i];\n      array[i] = t;\n    }\n    return array;\n  }\n  function forEach(callback) {\n    var i, j, t;\n    for (i = array.length - 1; i > 0; --i) {\n      j = localRandom.next(i + 1); // i inclusive\n      t = array[j];\n      array[j] = array[i];\n      array[i] = t;\n      callback(t);\n    }\n    if (array.length) {\n      callback(array[0]);\n    }\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "random", "randomIterator", "inputSeed", "seed", "Date", "Generator", "prototype", "next", "nextDouble", "uniform", "gaussian", "r", "x", "y", "Math", "sqrt", "log", "levy", "beta", "sigma", "pow", "gamma", "sin", "PI", "abs", "z", "E", "maxValue", "floor", "array", "customRandom", "localRandom", "Error", "for<PERSON>ach", "shuffle", "i", "j", "t", "length", "callback"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.random/index.js"], "sourcesContent": ["module.exports = random;\n\nmodule.exports.random = random,\nmodule.exports.randomIterator = randomIterator\n\n/**\n * Creates seeded PRNG with two methods:\n *   next() and nextDouble()\n */\nfunction random(inputSeed) {\n  var seed = typeof inputSeed === 'number' ? inputSeed : (+new Date());\n  return new Generator(seed)\n}\n\nfunction Generator(seed) {\n  this.seed = seed;\n}\n\n/**\n  * Generates random integer number in the range from 0 (inclusive) to maxValue (exclusive)\n  *\n  * @param maxValue Number REQUIRED. Omitting this number will result in NaN values from PRNG.\n  */\nGenerator.prototype.next = next;\n\n/**\n  * Generates random double number in the range from 0 (inclusive) to 1 (exclusive)\n  * This function is the same as Math.random() (except that it could be seeded)\n  */\nGenerator.prototype.nextDouble = nextDouble;\n\n/**\n * Returns a random real number from uniform distribution in [0, 1)\n */\nGenerator.prototype.uniform = nextDouble;\n\n/**\n * Returns a random real number from a Gaussian distribution\n * with 0 as a mean, and 1 as standard deviation u ~ N(0,1)\n */\nGenerator.prototype.gaussian = gaussian;\n\n/**\n * Returns a floating-point, pseudo-random number that's greater than \n * or equal to 0 and less than 1, with approximately uniform distribution over that range\n * \n * Note: This method is the same as nextDouble(), but is here for\n * compatibility with similar Math.random()\n */\nGenerator.prototype.random = nextDouble;\n\nfunction gaussian() {\n  // use the polar form of the Box-Muller transform\n  // based on https://introcs.cs.princeton.edu/java/23recursion/StdRandom.java\n  var r, x, y;\n  do {\n    x = this.nextDouble() * 2 - 1;\n    y = this.nextDouble() * 2 - 1;\n    r = x * x + y * y;\n  } while (r >= 1 || r === 0);\n\n  return x * Math.sqrt(-2 * Math.log(r)/r);\n}\n\n/**\n * See https://twitter.com/anvaka/status/1296182534150135808\n */\nGenerator.prototype.levy = levy;\n\nfunction levy() {\n  var beta = 3 / 2;\n  var sigma = Math.pow(\n      gamma( 1 + beta ) * Math.sin(Math.PI * beta / 2) / \n        (gamma((1 + beta) / 2) * beta * Math.pow(2, (beta - 1) / 2)),\n      1/beta\n  );\n  return this.gaussian() * sigma / Math.pow(Math.abs(this.gaussian()), 1/beta);\n}\n\n// gamma function approximation\nfunction gamma(z) {\n  return Math.sqrt(2 * Math.PI / z) * Math.pow((1 / Math.E) * (z + 1 / (12 * z - 1 / (10 * z))), z);\n}\n\nfunction nextDouble() {\n  var seed = this.seed;\n  // Robert Jenkins' 32 bit integer hash function.\n  seed = ((seed + 0x7ed55d16) + (seed << 12)) & 0xffffffff;\n  seed = ((seed ^ 0xc761c23c) ^ (seed >>> 19)) & 0xffffffff;\n  seed = ((seed + 0x165667b1) + (seed << 5)) & 0xffffffff;\n  seed = ((seed + 0xd3a2646c) ^ (seed << 9)) & 0xffffffff;\n  seed = ((seed + 0xfd7046c5) + (seed << 3)) & 0xffffffff;\n  seed = ((seed ^ 0xb55a4f09) ^ (seed >>> 16)) & 0xffffffff;\n  this.seed = seed;\n  return (seed & 0xfffffff) / 0x10000000;\n}\n\nfunction next(maxValue) {\n  return Math.floor(this.nextDouble() * maxValue);\n}\n\n/*\n * Creates iterator over array, which returns items of array in random order\n * Time complexity is guaranteed to be O(n);\n */\nfunction randomIterator(array, customRandom) {\n  var localRandom = customRandom || random();\n  if (typeof localRandom.next !== 'function') {\n    throw new Error('customRandom does not match expected API: next() function is missing');\n  }\n\n  return {\n    /**\n     * Visits every single element of a collection once, in a random order.\n     * Note: collection is modified in place.\n     */\n    forEach: forEach,\n\n    /**\n     * Shuffles array randomly, in place.\n     */\n    shuffle: shuffle\n  };\n\n  function shuffle() {\n    var i, j, t;\n    for (i = array.length - 1; i > 0; --i) {\n      j = localRandom.next(i + 1); // i inclusive\n      t = array[j];\n      array[j] = array[i];\n      array[i] = t;\n    }\n\n    return array;\n  }\n\n  function forEach(callback) {\n    var i, j, t;\n    for (i = array.length - 1; i > 0; --i) {\n      j = localRandom.next(i + 1); // i inclusive\n      t = array[j];\n      array[j] = array[i];\n      array[i] = t;\n\n      callback(t);\n    }\n\n    if (array.length) {\n      callback(array[0]);\n    }\n  }\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvBF,MAAM,CAACC,OAAO,CAACC,MAAM,GAAGA,MAAM,EAC9BF,MAAM,CAACC,OAAO,CAACE,cAAc,GAAGA,cAAc;;AAE9C;AACA;AACA;AACA;AACA,SAASD,MAAMA,CAACE,SAAS,EAAE;EACzB,IAAIC,IAAI,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAI,CAAC,IAAIE,IAAI,CAAC,CAAE;EACpE,OAAO,IAAIC,SAAS,CAACF,IAAI,CAAC;AAC5B;AAEA,SAASE,SAASA,CAACF,IAAI,EAAE;EACvB,IAAI,CAACA,IAAI,GAAGA,IAAI;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACAE,SAAS,CAACC,SAAS,CAACC,IAAI,GAAGA,IAAI;;AAE/B;AACA;AACA;AACA;AACAF,SAAS,CAACC,SAAS,CAACE,UAAU,GAAGA,UAAU;;AAE3C;AACA;AACA;AACAH,SAAS,CAACC,SAAS,CAACG,OAAO,GAAGD,UAAU;;AAExC;AACA;AACA;AACA;AACAH,SAAS,CAACC,SAAS,CAACI,QAAQ,GAAGA,QAAQ;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACAL,SAAS,CAACC,SAAS,CAACN,MAAM,GAAGQ,UAAU;AAEvC,SAASE,QAAQA,CAAA,EAAG;EAClB;EACA;EACA,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC;EACX,GAAG;IACDD,CAAC,GAAG,IAAI,CAACJ,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7BK,CAAC,GAAG,IAAI,CAACL,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7BG,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;EACnB,CAAC,QAAQF,CAAC,IAAI,CAAC,IAAIA,CAAC,KAAK,CAAC;EAE1B,OAAOC,CAAC,GAAGE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,GAACA,CAAC,CAAC;AAC1C;;AAEA;AACA;AACA;AACAN,SAAS,CAACC,SAAS,CAACW,IAAI,GAAGA,IAAI;AAE/B,SAASA,IAAIA,CAAA,EAAG;EACd,IAAIC,IAAI,GAAG,CAAC,GAAG,CAAC;EAChB,IAAIC,KAAK,GAAGL,IAAI,CAACM,GAAG,CAChBC,KAAK,CAAE,CAAC,GAAGH,IAAK,CAAC,GAAGJ,IAAI,CAACQ,GAAG,CAACR,IAAI,CAACS,EAAE,GAAGL,IAAI,GAAG,CAAC,CAAC,IAC7CG,KAAK,CAAC,CAAC,CAAC,GAAGH,IAAI,IAAI,CAAC,CAAC,GAAGA,IAAI,GAAGJ,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE,CAACF,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAC9D,CAAC,GAACA,IACN,CAAC;EACD,OAAO,IAAI,CAACR,QAAQ,CAAC,CAAC,GAAGS,KAAK,GAAGL,IAAI,CAACM,GAAG,CAACN,IAAI,CAACU,GAAG,CAAC,IAAI,CAACd,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAACQ,IAAI,CAAC;AAC9E;;AAEA;AACA,SAASG,KAAKA,CAACI,CAAC,EAAE;EAChB,OAAOX,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGD,IAAI,CAACS,EAAE,GAAGE,CAAC,CAAC,GAAGX,IAAI,CAACM,GAAG,CAAE,CAAC,GAAGN,IAAI,CAACY,CAAC,IAAKD,CAAC,GAAG,CAAC,IAAI,EAAE,GAAGA,CAAC,GAAG,CAAC,IAAI,EAAE,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC;AACnG;AAEA,SAASjB,UAAUA,CAAA,EAAG;EACpB,IAAIL,IAAI,GAAG,IAAI,CAACA,IAAI;EACpB;EACAA,IAAI,GAAKA,IAAI,GAAG,UAAU,IAAKA,IAAI,IAAI,EAAE,CAAC,GAAI,UAAU;EACxDA,IAAI,GAAG,CAAEA,IAAI,GAAG,UAAU,GAAKA,IAAI,KAAK,EAAG,IAAI,UAAU;EACzDA,IAAI,GAAKA,IAAI,GAAG,UAAU,IAAKA,IAAI,IAAI,CAAC,CAAC,GAAI,UAAU;EACvDA,IAAI,GAAG,CAAEA,IAAI,GAAG,UAAU,GAAKA,IAAI,IAAI,CAAE,IAAI,UAAU;EACvDA,IAAI,GAAKA,IAAI,GAAG,UAAU,IAAKA,IAAI,IAAI,CAAC,CAAC,GAAI,UAAU;EACvDA,IAAI,GAAG,CAAEA,IAAI,GAAG,UAAU,GAAKA,IAAI,KAAK,EAAG,IAAI,UAAU;EACzD,IAAI,CAACA,IAAI,GAAGA,IAAI;EAChB,OAAO,CAACA,IAAI,GAAG,SAAS,IAAI,UAAU;AACxC;AAEA,SAASI,IAAIA,CAACoB,QAAQ,EAAE;EACtB,OAAOb,IAAI,CAACc,KAAK,CAAC,IAAI,CAACpB,UAAU,CAAC,CAAC,GAAGmB,QAAQ,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA,SAAS1B,cAAcA,CAAC4B,KAAK,EAAEC,YAAY,EAAE;EAC3C,IAAIC,WAAW,GAAGD,YAAY,IAAI9B,MAAM,CAAC,CAAC;EAC1C,IAAI,OAAO+B,WAAW,CAACxB,IAAI,KAAK,UAAU,EAAE;IAC1C,MAAM,IAAIyB,KAAK,CAAC,sEAAsE,CAAC;EACzF;EAEA,OAAO;IACL;AACJ;AACA;AACA;IACIC,OAAO,EAAEA,OAAO;IAEhB;AACJ;AACA;IACIC,OAAO,EAAEA;EACX,CAAC;EAED,SAASA,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC;IACX,KAAKF,CAAC,GAAGN,KAAK,CAACS,MAAM,GAAG,CAAC,EAAEH,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACrCC,CAAC,GAAGL,WAAW,CAACxB,IAAI,CAAC4B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7BE,CAAC,GAAGR,KAAK,CAACO,CAAC,CAAC;MACZP,KAAK,CAACO,CAAC,CAAC,GAAGP,KAAK,CAACM,CAAC,CAAC;MACnBN,KAAK,CAACM,CAAC,CAAC,GAAGE,CAAC;IACd;IAEA,OAAOR,KAAK;EACd;EAEA,SAASI,OAAOA,CAACM,QAAQ,EAAE;IACzB,IAAIJ,CAAC,EAAEC,CAAC,EAAEC,CAAC;IACX,KAAKF,CAAC,GAAGN,KAAK,CAACS,MAAM,GAAG,CAAC,EAAEH,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACrCC,CAAC,GAAGL,WAAW,CAACxB,IAAI,CAAC4B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7BE,CAAC,GAAGR,KAAK,CAACO,CAAC,CAAC;MACZP,KAAK,CAACO,CAAC,CAAC,GAAGP,KAAK,CAACM,CAAC,CAAC;MACnBN,KAAK,CAACM,CAAC,CAAC,GAAGE,CAAC;MAEZE,QAAQ,CAACF,CAAC,CAAC;IACb;IAEA,IAAIR,KAAK,CAACS,MAAM,EAAE;MAChBC,QAAQ,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
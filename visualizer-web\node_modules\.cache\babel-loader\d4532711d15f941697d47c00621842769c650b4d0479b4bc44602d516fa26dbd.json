{"ast": null, "code": "import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\nexport var root = [null];\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\nfunction selection_selection() {\n  return this;\n}\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\nexport default selection;", "map": {"version": 3, "names": ["selection_select", "selection_selectAll", "selection_select<PERSON><PERSON>d", "selection_select<PERSON><PERSON><PERSON><PERSON>", "selection_filter", "selection_data", "selection_enter", "selection_exit", "selection_join", "selection_merge", "selection_order", "selection_sort", "selection_call", "selection_nodes", "selection_node", "selection_size", "selection_empty", "selection_each", "selection_attr", "selection_style", "selection_property", "selection_classed", "selection_text", "selection_html", "selection_raise", "selection_lower", "selection_append", "selection_insert", "selection_remove", "selection_clone", "selection_datum", "selection_on", "selection_dispatch", "selection_iterator", "root", "Selection", "groups", "parents", "_groups", "_parents", "selection", "document", "documentElement", "selection_selection", "prototype", "constructor", "select", "selectAll", "<PERSON><PERSON><PERSON><PERSON>", "select<PERSON><PERSON><PERSON><PERSON>", "filter", "data", "enter", "exit", "join", "merge", "order", "sort", "call", "nodes", "node", "size", "empty", "each", "attr", "style", "property", "classed", "text", "html", "raise", "lower", "append", "insert", "remove", "clone", "datum", "on", "dispatch", "Symbol", "iterator"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selection/index.js"], "sourcesContent": ["import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,aAAa;AAC1C,OAAOC,mBAAmB,MAAM,gBAAgB;AAChD,OAAOC,qBAAqB,MAAM,kBAAkB;AACpD,OAAOC,wBAAwB,MAAM,qBAAqB;AAC1D,OAAOC,gBAAgB,MAAM,aAAa;AAC1C,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,kBAAkB,MAAM,eAAe;AAC9C,OAAOC,iBAAiB,MAAM,cAAc;AAC5C,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,gBAAgB,MAAM,aAAa;AAC1C,OAAOC,gBAAgB,MAAM,aAAa;AAC1C,OAAOC,gBAAgB,MAAM,aAAa;AAC1C,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,eAAe,MAAM,YAAY;AACxC,OAAOC,YAAY,MAAM,SAAS;AAClC,OAAOC,kBAAkB,MAAM,eAAe;AAC9C,OAAOC,kBAAkB,MAAM,eAAe;AAE9C,OAAO,IAAIC,IAAI,GAAG,CAAC,IAAI,CAAC;AAExB,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACzC,IAAI,CAACC,OAAO,GAAGF,MAAM;EACrB,IAAI,CAACG,QAAQ,GAAGF,OAAO;AACzB;AAEA,SAASG,SAASA,CAAA,EAAG;EACnB,OAAO,IAAIL,SAAS,CAAC,CAAC,CAACM,QAAQ,CAACC,eAAe,CAAC,CAAC,EAAER,IAAI,CAAC;AAC1D;AAEA,SAASS,mBAAmBA,CAAA,EAAG;EAC7B,OAAO,IAAI;AACb;AAEAR,SAAS,CAACS,SAAS,GAAGJ,SAAS,CAACI,SAAS,GAAG;EAC1CC,WAAW,EAAEV,SAAS;EACtBW,MAAM,EAAE9C,gBAAgB;EACxB+C,SAAS,EAAE9C,mBAAmB;EAC9B+C,WAAW,EAAE9C,qBAAqB;EAClC+C,cAAc,EAAE9C,wBAAwB;EACxC+C,MAAM,EAAE9C,gBAAgB;EACxB+C,IAAI,EAAE9C,cAAc;EACpB+C,KAAK,EAAE9C,eAAe;EACtB+C,IAAI,EAAE9C,cAAc;EACpB+C,IAAI,EAAE9C,cAAc;EACpB+C,KAAK,EAAE9C,eAAe;EACtB+B,SAAS,EAAEG,mBAAmB;EAC9Ba,KAAK,EAAE9C,eAAe;EACtB+C,IAAI,EAAE9C,cAAc;EACpB+C,IAAI,EAAE9C,cAAc;EACpB+C,KAAK,EAAE9C,eAAe;EACtB+C,IAAI,EAAE9C,cAAc;EACpB+C,IAAI,EAAE9C,cAAc;EACpB+C,KAAK,EAAE9C,eAAe;EACtB+C,IAAI,EAAE9C,cAAc;EACpB+C,IAAI,EAAE9C,cAAc;EACpB+C,KAAK,EAAE9C,eAAe;EACtB+C,QAAQ,EAAE9C,kBAAkB;EAC5B+C,OAAO,EAAE9C,iBAAiB;EAC1B+C,IAAI,EAAE9C,cAAc;EACpB+C,IAAI,EAAE9C,cAAc;EACpB+C,KAAK,EAAE9C,eAAe;EACtB+C,KAAK,EAAE9C,eAAe;EACtB+C,MAAM,EAAE9C,gBAAgB;EACxB+C,MAAM,EAAE9C,gBAAgB;EACxB+C,MAAM,EAAE9C,gBAAgB;EACxB+C,KAAK,EAAE9C,eAAe;EACtB+C,KAAK,EAAE9C,eAAe;EACtB+C,EAAE,EAAE9C,YAAY;EAChB+C,QAAQ,EAAE9C,kBAAkB;EAC5B,CAAC+C,MAAM,CAACC,QAAQ,GAAG/C;AACrB,CAAC;AAED,eAAeO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
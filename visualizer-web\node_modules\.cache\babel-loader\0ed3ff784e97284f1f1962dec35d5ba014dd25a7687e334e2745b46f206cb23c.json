{"ast": null, "code": "module.exports = function eventify(subject) {\n  validateSubject(subject);\n  var eventsStorage = createEventsStorage(subject);\n  subject.on = eventsStorage.on;\n  subject.off = eventsStorage.off;\n  subject.fire = eventsStorage.fire;\n  return subject;\n};\nfunction createEventsStorage(subject) {\n  // Store all event listeners to this hash. Key is event name, value is array\n  // of callback records.\n  //\n  // A callback record consists of callback function and its optional context:\n  // { 'eventName' => [{callback: function, ctx: object}] }\n  var registeredEvents = Object.create(null);\n  return {\n    on: function (eventName, callback, ctx) {\n      if (typeof callback !== 'function') {\n        throw new Error('callback is expected to be a function');\n      }\n      var handlers = registeredEvents[eventName];\n      if (!handlers) {\n        handlers = registeredEvents[eventName] = [];\n      }\n      handlers.push({\n        callback: callback,\n        ctx: ctx\n      });\n      return subject;\n    },\n    off: function (eventName, callback) {\n      var wantToRemoveAll = typeof eventName === 'undefined';\n      if (wantToRemoveAll) {\n        // Killing old events storage should be enough in this case:\n        registeredEvents = Object.create(null);\n        return subject;\n      }\n      if (registeredEvents[eventName]) {\n        var deleteAllCallbacksForEvent = typeof callback !== 'function';\n        if (deleteAllCallbacksForEvent) {\n          delete registeredEvents[eventName];\n        } else {\n          var callbacks = registeredEvents[eventName];\n          for (var i = 0; i < callbacks.length; ++i) {\n            if (callbacks[i].callback === callback) {\n              callbacks.splice(i, 1);\n            }\n          }\n        }\n      }\n      return subject;\n    },\n    fire: function (eventName) {\n      var callbacks = registeredEvents[eventName];\n      if (!callbacks) {\n        return subject;\n      }\n      var fireArguments;\n      if (arguments.length > 1) {\n        fireArguments = Array.prototype.splice.call(arguments, 1);\n      }\n      for (var i = 0; i < callbacks.length; ++i) {\n        var callbackInfo = callbacks[i];\n        callbackInfo.callback.apply(callbackInfo.ctx, fireArguments);\n      }\n      return subject;\n    }\n  };\n}\nfunction validateSubject(subject) {\n  if (!subject) {\n    throw new Error('Eventify cannot use falsy object as events subject');\n  }\n  var reservedWords = ['on', 'fire', 'off'];\n  for (var i = 0; i < reservedWords.length; ++i) {\n    if (subject.hasOwnProperty(reservedWords[i])) {\n      throw new Error(\"Subject cannot be eventified, since it already has property '\" + reservedWords[i] + \"'\");\n    }\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "eventify", "subject", "validateSubject", "eventsStorage", "createEventsStorage", "on", "off", "fire", "registeredEvents", "Object", "create", "eventName", "callback", "ctx", "Error", "handlers", "push", "wantToRemoveAll", "deleteAllCallbacksForEvent", "callbacks", "i", "length", "splice", "fireArguments", "arguments", "Array", "prototype", "call", "callbackInfo", "apply", "reservedWords", "hasOwnProperty"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.events/index.js"], "sourcesContent": ["module.exports = function eventify(subject) {\n  validateSubject(subject);\n\n  var eventsStorage = createEventsStorage(subject);\n  subject.on = eventsStorage.on;\n  subject.off = eventsStorage.off;\n  subject.fire = eventsStorage.fire;\n  return subject;\n};\n\nfunction createEventsStorage(subject) {\n  // Store all event listeners to this hash. Key is event name, value is array\n  // of callback records.\n  //\n  // A callback record consists of callback function and its optional context:\n  // { 'eventName' => [{callback: function, ctx: object}] }\n  var registeredEvents = Object.create(null);\n\n  return {\n    on: function (eventName, callback, ctx) {\n      if (typeof callback !== 'function') {\n        throw new Error('callback is expected to be a function');\n      }\n      var handlers = registeredEvents[eventName];\n      if (!handlers) {\n        handlers = registeredEvents[eventName] = [];\n      }\n      handlers.push({callback: callback, ctx: ctx});\n\n      return subject;\n    },\n\n    off: function (eventName, callback) {\n      var wantToRemoveAll = (typeof eventName === 'undefined');\n      if (wantToRemoveAll) {\n        // Killing old events storage should be enough in this case:\n        registeredEvents = Object.create(null);\n        return subject;\n      }\n\n      if (registeredEvents[eventName]) {\n        var deleteAllCallbacksForEvent = (typeof callback !== 'function');\n        if (deleteAllCallbacksForEvent) {\n          delete registeredEvents[eventName];\n        } else {\n          var callbacks = registeredEvents[eventName];\n          for (var i = 0; i < callbacks.length; ++i) {\n            if (callbacks[i].callback === callback) {\n              callbacks.splice(i, 1);\n            }\n          }\n        }\n      }\n\n      return subject;\n    },\n\n    fire: function (eventName) {\n      var callbacks = registeredEvents[eventName];\n      if (!callbacks) {\n        return subject;\n      }\n\n      var fireArguments;\n      if (arguments.length > 1) {\n        fireArguments = Array.prototype.splice.call(arguments, 1);\n      }\n      for(var i = 0; i < callbacks.length; ++i) {\n        var callbackInfo = callbacks[i];\n        callbackInfo.callback.apply(callbackInfo.ctx, fireArguments);\n      }\n\n      return subject;\n    }\n  };\n}\n\nfunction validateSubject(subject) {\n  if (!subject) {\n    throw new Error('Eventify cannot use falsy object as events subject');\n  }\n  var reservedWords = ['on', 'fire', 'off'];\n  for (var i = 0; i < reservedWords.length; ++i) {\n    if (subject.hasOwnProperty(reservedWords[i])) {\n      throw new Error(\"Subject cannot be eventified, since it already has property '\" + reservedWords[i] + \"'\");\n    }\n  }\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,OAAO,EAAE;EAC1CC,eAAe,CAACD,OAAO,CAAC;EAExB,IAAIE,aAAa,GAAGC,mBAAmB,CAACH,OAAO,CAAC;EAChDA,OAAO,CAACI,EAAE,GAAGF,aAAa,CAACE,EAAE;EAC7BJ,OAAO,CAACK,GAAG,GAAGH,aAAa,CAACG,GAAG;EAC/BL,OAAO,CAACM,IAAI,GAAGJ,aAAa,CAACI,IAAI;EACjC,OAAON,OAAO;AAChB,CAAC;AAED,SAASG,mBAAmBA,CAACH,OAAO,EAAE;EACpC;EACA;EACA;EACA;EACA;EACA,IAAIO,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAE1C,OAAO;IACLL,EAAE,EAAE,SAAAA,CAAUM,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAE;MACtC,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIE,KAAK,CAAC,uCAAuC,CAAC;MAC1D;MACA,IAAIC,QAAQ,GAAGP,gBAAgB,CAACG,SAAS,CAAC;MAC1C,IAAI,CAACI,QAAQ,EAAE;QACbA,QAAQ,GAAGP,gBAAgB,CAACG,SAAS,CAAC,GAAG,EAAE;MAC7C;MACAI,QAAQ,CAACC,IAAI,CAAC;QAACJ,QAAQ,EAAEA,QAAQ;QAAEC,GAAG,EAAEA;MAAG,CAAC,CAAC;MAE7C,OAAOZ,OAAO;IAChB,CAAC;IAEDK,GAAG,EAAE,SAAAA,CAAUK,SAAS,EAAEC,QAAQ,EAAE;MAClC,IAAIK,eAAe,GAAI,OAAON,SAAS,KAAK,WAAY;MACxD,IAAIM,eAAe,EAAE;QACnB;QACAT,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QACtC,OAAOT,OAAO;MAChB;MAEA,IAAIO,gBAAgB,CAACG,SAAS,CAAC,EAAE;QAC/B,IAAIO,0BAA0B,GAAI,OAAON,QAAQ,KAAK,UAAW;QACjE,IAAIM,0BAA0B,EAAE;UAC9B,OAAOV,gBAAgB,CAACG,SAAS,CAAC;QACpC,CAAC,MAAM;UACL,IAAIQ,SAAS,GAAGX,gBAAgB,CAACG,SAAS,CAAC;UAC3C,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAE,EAAED,CAAC,EAAE;YACzC,IAAID,SAAS,CAACC,CAAC,CAAC,CAACR,QAAQ,KAAKA,QAAQ,EAAE;cACtCO,SAAS,CAACG,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;YACxB;UACF;QACF;MACF;MAEA,OAAOnB,OAAO;IAChB,CAAC;IAEDM,IAAI,EAAE,SAAAA,CAAUI,SAAS,EAAE;MACzB,IAAIQ,SAAS,GAAGX,gBAAgB,CAACG,SAAS,CAAC;MAC3C,IAAI,CAACQ,SAAS,EAAE;QACd,OAAOlB,OAAO;MAChB;MAEA,IAAIsB,aAAa;MACjB,IAAIC,SAAS,CAACH,MAAM,GAAG,CAAC,EAAE;QACxBE,aAAa,GAAGE,KAAK,CAACC,SAAS,CAACJ,MAAM,CAACK,IAAI,CAACH,SAAS,EAAE,CAAC,CAAC;MAC3D;MACA,KAAI,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAE,EAAED,CAAC,EAAE;QACxC,IAAIQ,YAAY,GAAGT,SAAS,CAACC,CAAC,CAAC;QAC/BQ,YAAY,CAAChB,QAAQ,CAACiB,KAAK,CAACD,YAAY,CAACf,GAAG,EAAEU,aAAa,CAAC;MAC9D;MAEA,OAAOtB,OAAO;IAChB;EACF,CAAC;AACH;AAEA,SAASC,eAAeA,CAACD,OAAO,EAAE;EAChC,IAAI,CAACA,OAAO,EAAE;IACZ,MAAM,IAAIa,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,IAAIgB,aAAa,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC;EACzC,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,aAAa,CAACT,MAAM,EAAE,EAAED,CAAC,EAAE;IAC7C,IAAInB,OAAO,CAAC8B,cAAc,CAACD,aAAa,CAACV,CAAC,CAAC,CAAC,EAAE;MAC5C,MAAM,IAAIN,KAAK,CAAC,+DAA+D,GAAGgB,aAAa,CAACV,CAAC,CAAC,GAAG,GAAG,CAAC;IAC3G;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
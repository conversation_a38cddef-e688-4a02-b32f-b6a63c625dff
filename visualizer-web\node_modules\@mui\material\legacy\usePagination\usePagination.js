'use client';

import _extends from "@babel/runtime/helpers/esm/extends";
import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import useControlled from '@mui/utils/useControlled';
export default function usePagination() {
  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  // keep default values in sync with @default tags in Pagination.propTypes
  var _props$boundaryCount = props.boundaryCount,
    boundaryCount = _props$boundaryCount === void 0 ? 1 : _props$boundaryCount,
    _props$componentName = props.componentName,
    componentName = _props$componentName === void 0 ? 'usePagination' : _props$componentName,
    _props$count = props.count,
    count = _props$count === void 0 ? 1 : _props$count,
    _props$defaultPage = props.defaultPage,
    defaultPage = _props$defaultPage === void 0 ? 1 : _props$defaultPage,
    _props$disabled = props.disabled,
    disabled = _props$disabled === void 0 ? false : _props$disabled,
    _props$hideNextButton = props.hideNextButton,
    hideNextButton = _props$hideNextButton === void 0 ? false : _props$hideNextButton,
    _props$hidePrevButton = props.hidePrevButton,
    hidePrevButton = _props$hidePrevButton === void 0 ? false : _props$hidePrevButton,
    handleChange = props.onChange,
    pageProp = props.page,
    _props$showFirstButto = props.showFirstButton,
    showFirstButton = _props$showFirstButto === void 0 ? false : _props$showFirstButto,
    _props$showLastButton = props.showLastButton,
    showLastButton = _props$showLastButton === void 0 ? false : _props$showLastButton,
    _props$siblingCount = props.siblingCount,
    siblingCount = _props$siblingCount === void 0 ? 1 : _props$siblingCount,
    other = _objectWithoutProperties(props, ["boundaryCount", "componentName", "count", "defaultPage", "disabled", "hideNextButton", "hidePrevButton", "onChange", "page", "showFirstButton", "showLastButton", "siblingCount"]);
  var _useControlled = useControlled({
      controlled: pageProp,
      default: defaultPage,
      name: componentName,
      state: 'page'
    }),
    _useControlled2 = _slicedToArray(_useControlled, 2),
    page = _useControlled2[0],
    setPageState = _useControlled2[1];
  var handleClick = function handleClick(event, value) {
    if (!pageProp) {
      setPageState(value);
    }
    if (handleChange) {
      handleChange(event, value);
    }
  };

  // https://dev.to/namirsab/comment/2050
  var range = function range(start, end) {
    var length = end - start + 1;
    return Array.from({
      length: length
    }, function (_, i) {
      return start + i;
    });
  };
  var startPages = range(1, Math.min(boundaryCount, count));
  var endPages = range(Math.max(count - boundaryCount + 1, boundaryCount + 1), count);
  var siblingsStart = Math.max(Math.min(
  // Natural start
  page - siblingCount,
  // Lower boundary when page is high
  count - boundaryCount - siblingCount * 2 - 1),
  // Greater than startPages
  boundaryCount + 2);
  var siblingsEnd = Math.min(Math.max(
  // Natural end
  page + siblingCount,
  // Upper boundary when page is low
  boundaryCount + siblingCount * 2 + 2),
  // Less than endPages
  endPages.length > 0 ? endPages[0] - 2 : count - 1);

  // Basic list of items to render
  // for example itemList = ['first', 'previous', 1, 'ellipsis', 4, 5, 6, 'ellipsis', 10, 'next', 'last']
  var itemList = [].concat(_toConsumableArray(showFirstButton ? ['first'] : []), _toConsumableArray(hidePrevButton ? [] : ['previous']), _toConsumableArray(startPages), _toConsumableArray(siblingsStart > boundaryCount + 2 ? ['start-ellipsis'] : boundaryCount + 1 < count - boundaryCount ? [boundaryCount + 1] : []), _toConsumableArray(range(siblingsStart, siblingsEnd)), _toConsumableArray(siblingsEnd < count - boundaryCount - 1 ? ['end-ellipsis'] : count - boundaryCount > boundaryCount ? [count - boundaryCount] : []), _toConsumableArray(endPages), _toConsumableArray(hideNextButton ? [] : ['next']), _toConsumableArray(showLastButton ? ['last'] : []));

  // Map the button type to its page number
  var buttonPage = function buttonPage(type) {
    switch (type) {
      case 'first':
        return 1;
      case 'previous':
        return page - 1;
      case 'next':
        return page + 1;
      case 'last':
        return count;
      default:
        return null;
    }
  };

  // Convert the basic item list to PaginationItem props objects
  var items = itemList.map(function (item) {
    return typeof item === 'number' ? {
      onClick: function onClick(event) {
        handleClick(event, item);
      },
      type: 'page',
      page: item,
      selected: item === page,
      disabled: disabled,
      'aria-current': item === page ? 'true' : undefined
    } : {
      onClick: function onClick(event) {
        handleClick(event, buttonPage(item));
      },
      type: item,
      page: buttonPage(item),
      selected: false,
      disabled: disabled || item.indexOf('ellipsis') === -1 && (item === 'next' || item === 'last' ? page >= count : page <= 1)
    };
  });
  return _extends({
    items: items
  }, other);
}
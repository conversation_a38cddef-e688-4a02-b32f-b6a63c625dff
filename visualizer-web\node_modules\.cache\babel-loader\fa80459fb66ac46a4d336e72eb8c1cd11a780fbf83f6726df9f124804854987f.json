{"ast": null, "code": "const distance = (x1, y1, z1, x2, y2, z2) => Math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2 + (z1 - z2) ** 2);\nexport function findAllWithinRadius(x, y, z, radius) {\n  const result = [];\n  const xMin = x - radius;\n  const yMin = y - radius;\n  const zMin = z - radius;\n  const xMax = x + radius;\n  const yMax = y + radius;\n  const zMax = z + radius;\n  this.visit((node, x1, y1, z1, x2, y2, z2) => {\n    if (!node.length) {\n      do {\n        const d = node.data;\n        if (distance(x, y, z, this._x(d), this._y(d), this._z(d)) <= radius) {\n          result.push(d);\n        }\n      } while (node = node.next);\n    }\n    return x1 > xMax || y1 > yMax || z1 > zMax || x2 < xMin || y2 < yMin || z2 < zMin;\n  });\n  return result;\n}", "map": {"version": 3, "names": ["distance", "x1", "y1", "z1", "x2", "y2", "z2", "Math", "sqrt", "findAllWithinRadius", "x", "y", "z", "radius", "result", "xMin", "yMin", "zMin", "xMax", "yMax", "zMax", "visit", "node", "length", "d", "data", "_x", "_y", "_z", "push", "next"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/findAll.js"], "sourcesContent": ["const distance = (x1, y1, z1, x2, y2, z2) => Math.sqrt((x1-x2)**2 + (y1-y2)**2 + (z1-z2)**2);\n\nexport function findAllWithinRadius(x, y, z, radius) {\n  const result = [];\n\n  const xMin = x - radius;\n  const yMin = y - radius;\n  const zMin = z - radius;\n  const xMax = x + radius;\n  const yMax = y + radius;\n  const zMax = z + radius;\n\n  this.visit((node, x1, y1, z1, x2, y2, z2) => {\n    if (!node.length) {\n      do {\n        const d = node.data;\n        if (distance(x, y, z, this._x(d), this._y(d), this._z(d)) <= radius) {\n          result.push(d);\n        }\n      } while (node = node.next);\n    }\n    return x1 > xMax || y1 > yMax || z1 > zMax || x2 < xMin || y2 < yMin || z2 < zMin;\n  });\n\n  return result;\n}\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAKC,IAAI,CAACC,IAAI,CAAC,CAACP,EAAE,GAACG,EAAE,KAAG,CAAC,GAAG,CAACF,EAAE,GAACG,EAAE,KAAG,CAAC,GAAG,CAACF,EAAE,GAACG,EAAE,KAAG,CAAC,CAAC;AAE5F,OAAO,SAASG,mBAAmBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE;EACnD,MAAMC,MAAM,GAAG,EAAE;EAEjB,MAAMC,IAAI,GAAGL,CAAC,GAAGG,MAAM;EACvB,MAAMG,IAAI,GAAGL,CAAC,GAAGE,MAAM;EACvB,MAAMI,IAAI,GAAGL,CAAC,GAAGC,MAAM;EACvB,MAAMK,IAAI,GAAGR,CAAC,GAAGG,MAAM;EACvB,MAAMM,IAAI,GAAGR,CAAC,GAAGE,MAAM;EACvB,MAAMO,IAAI,GAAGR,CAAC,GAAGC,MAAM;EAEvB,IAAI,CAACQ,KAAK,CAAC,CAACC,IAAI,EAAErB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;IAC3C,IAAI,CAACgB,IAAI,CAACC,MAAM,EAAE;MAChB,GAAG;QACD,MAAMC,CAAC,GAAGF,IAAI,CAACG,IAAI;QACnB,IAAIzB,QAAQ,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACc,EAAE,CAACF,CAAC,CAAC,EAAE,IAAI,CAACG,EAAE,CAACH,CAAC,CAAC,EAAE,IAAI,CAACI,EAAE,CAACJ,CAAC,CAAC,CAAC,IAAIX,MAAM,EAAE;UACnEC,MAAM,CAACe,IAAI,CAACL,CAAC,CAAC;QAChB;MACF,CAAC,QAAQF,IAAI,GAAGA,IAAI,CAACQ,IAAI;IAC3B;IACA,OAAO7B,EAAE,GAAGiB,IAAI,IAAIhB,EAAE,GAAGiB,IAAI,IAAIhB,EAAE,GAAGiB,IAAI,IAAIhB,EAAE,GAAGW,IAAI,IAAIV,EAAE,GAAGW,IAAI,IAAIV,EAAE,GAAGW,IAAI;EACnF,CAAC,CAAC;EAEF,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
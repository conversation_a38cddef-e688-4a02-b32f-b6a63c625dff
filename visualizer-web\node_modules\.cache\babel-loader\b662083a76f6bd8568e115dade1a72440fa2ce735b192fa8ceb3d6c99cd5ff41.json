{"ast": null, "code": "export default function (t) {\n  t = Math.max(0, Math.min(1, t));\n  return \"rgb(\" + Math.max(0, Math.min(255, Math.round(34.61 + t * (1172.33 - t * (10793.56 - t * (33300.12 - t * (38394.49 - t * 14825.05))))))) + \", \" + Math.max(0, Math.min(255, Math.round(23.31 + t * (557.33 + t * (1225.33 - t * (3574.96 - t * (1073.77 + t * 707.56))))))) + \", \" + Math.max(0, Math.min(255, Math.round(27.2 + t * (3211.1 - t * (15327.97 - t * (27814 - t * (22569.18 - t * 6838.66))))))) + \")\";\n}", "map": {"version": 3, "names": ["t", "Math", "max", "min", "round"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-multi/turbo.js"], "sourcesContent": ["export default function(t) {\n  t = Math.max(0, Math.min(1, t));\n  return \"rgb(\"\n      + Math.max(0, Math.min(255, Math.round(34.61 + t * (1172.33 - t * (10793.56 - t * (33300.12 - t * (38394.49 - t * 14825.05))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(23.31 + t * (557.33 + t * (1225.33 - t * (3574.96 - t * (1073.77 + t * 707.56))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(27.2 + t * (3211.1 - t * (15327.97 - t * (27814 - t * (22569.18 - t * 6838.66)))))))\n      + \")\";\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzBA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,CAAC,CAAC,CAAC;EAC/B,OAAO,MAAM,GACPC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEF,IAAI,CAACG,KAAK,CAAC,KAAK,GAAGJ,CAAC,IAAI,OAAO,GAAGA,CAAC,IAAI,QAAQ,GAAGA,CAAC,IAAI,QAAQ,GAAGA,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GACtIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEF,IAAI,CAACG,KAAK,CAAC,KAAK,GAAGJ,CAAC,IAAI,MAAM,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAChIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEF,IAAI,CAACG,KAAK,CAAC,IAAI,GAAGJ,CAAC,IAAI,MAAM,GAAGA,CAAC,IAAI,QAAQ,GAAGA,CAAC,IAAI,KAAK,GAAGA,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACzH,GAAG;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import Half from \"./half.js\";\nexport default function (callback) {\n  var halves = [],\n    next = [],\n    q;\n  if (this._root) halves.push(new Half(this._root, this._x0, this._x1));\n  while (q = halves.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child,\n        x0 = q.x0,\n        x1 = q.x1,\n        xm = (x0 + x1) / 2;\n      if (child = node[0]) halves.push(new Half(child, x0, xm));\n      if (child = node[1]) halves.push(new Half(child, xm, x1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.x1);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Half", "callback", "halves", "next", "q", "_root", "push", "_x0", "_x1", "pop", "node", "length", "child", "x0", "x1", "xm"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-binarytree/src/visitAfter.js"], "sourcesContent": ["import Half from \"./half.js\";\n\nexport default function(callback) {\n  var halves = [], next = [], q;\n  if (this._root) halves.push(new Half(this._root, this._x0, this._x1));\n  while (q = halves.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, x1 = q.x1, xm = (x0 + x1) / 2;\n      if (child = node[0]) halves.push(new Half(child, x0, xm));\n      if (child = node[1]) halves.push(new Half(child, xm, x1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.x1);\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,QAAQ,EAAE;EAChC,IAAIC,MAAM,GAAG,EAAE;IAAEC,IAAI,GAAG,EAAE;IAAEC,CAAC;EAC7B,IAAI,IAAI,CAACC,KAAK,EAAEH,MAAM,CAACI,IAAI,CAAC,IAAIN,IAAI,CAAC,IAAI,CAACK,KAAK,EAAE,IAAI,CAACE,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;EACrE,OAAOJ,CAAC,GAAGF,MAAM,CAACO,GAAG,CAAC,CAAC,EAAE;IACvB,IAAIC,IAAI,GAAGN,CAAC,CAACM,IAAI;IACjB,IAAIA,IAAI,CAACC,MAAM,EAAE;MACf,IAAIC,KAAK;QAAEC,EAAE,GAAGT,CAAC,CAACS,EAAE;QAAEC,EAAE,GAAGV,CAAC,CAACU,EAAE;QAAEC,EAAE,GAAG,CAACF,EAAE,GAAGC,EAAE,IAAI,CAAC;MACnD,IAAIF,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAER,MAAM,CAACI,IAAI,CAAC,IAAIN,IAAI,CAACY,KAAK,EAAEC,EAAE,EAAEE,EAAE,CAAC,CAAC;MACzD,IAAIH,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAER,MAAM,CAACI,IAAI,CAAC,IAAIN,IAAI,CAACY,KAAK,EAAEG,EAAE,EAAED,EAAE,CAAC,CAAC;IAC3D;IACAX,IAAI,CAACG,IAAI,CAACF,CAAC,CAAC;EACd;EACA,OAAOA,CAAC,GAAGD,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE;IACrBR,QAAQ,CAACG,CAAC,CAACM,IAAI,EAAEN,CAAC,CAACS,EAAE,EAAET,CAAC,CAACU,EAAE,CAAC;EAC9B;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
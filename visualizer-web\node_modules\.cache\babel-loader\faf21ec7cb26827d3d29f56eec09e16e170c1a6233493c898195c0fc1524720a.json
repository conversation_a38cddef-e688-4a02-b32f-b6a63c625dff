{"ast": null, "code": "var noop = {\n  value: () => {}\n};\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || t in _ || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\nfunction Dispatch(_) {\n  this._ = _;\n}\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function (t) {\n    var name = \"\",\n      i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {\n      type: t,\n      name: name\n    };\n  });\n}\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function (typename, callback) {\n    var _ = this._,\n      T = parseTypenames(typename + \"\", _),\n      t,\n      i = -1,\n      n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n    return this;\n  },\n  copy: function () {\n    var copy = {},\n      _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function (type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function (type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({\n    name: name,\n    value: callback\n  });\n  return type;\n}\nexport default dispatch;", "map": {"version": 3, "names": ["noop", "value", "dispatch", "i", "n", "arguments", "length", "_", "t", "test", "Error", "Dispatch", "parseTypenames", "typenames", "types", "trim", "split", "map", "name", "indexOf", "slice", "hasOwnProperty", "type", "prototype", "constructor", "on", "typename", "callback", "T", "get", "set", "copy", "call", "that", "args", "Array", "apply", "c", "concat", "push"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-dispatch/src/dispatch.js"], "sourcesContent": ["var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAG;EAACC,KAAK,EAAEA,CAAA,KAAM,CAAC;AAAC,CAAC;AAE5B,SAASC,QAAQA,CAAA,EAAG;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,EAAEL,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC3D,IAAI,EAAEK,CAAC,GAAGH,SAAS,CAACF,CAAC,CAAC,GAAG,EAAE,CAAC,IAAKK,CAAC,IAAID,CAAE,IAAI,OAAO,CAACE,IAAI,CAACD,CAAC,CAAC,EAAE,MAAM,IAAIE,KAAK,CAAC,gBAAgB,GAAGF,CAAC,CAAC;IAClGD,CAAC,CAACC,CAAC,CAAC,GAAG,EAAE;EACX;EACA,OAAO,IAAIG,QAAQ,CAACJ,CAAC,CAAC;AACxB;AAEA,SAASI,QAAQA,CAACJ,CAAC,EAAE;EACnB,IAAI,CAACA,CAAC,GAAGA,CAAC;AACZ;AAEA,SAASK,cAAcA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACxC,OAAOD,SAAS,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,UAAST,CAAC,EAAE;IACrD,IAAIU,IAAI,GAAG,EAAE;MAAEf,CAAC,GAAGK,CAAC,CAACW,OAAO,CAAC,GAAG,CAAC;IACjC,IAAIhB,CAAC,IAAI,CAAC,EAAEe,IAAI,GAAGV,CAAC,CAACY,KAAK,CAACjB,CAAC,GAAG,CAAC,CAAC,EAAEK,CAAC,GAAGA,CAAC,CAACY,KAAK,CAAC,CAAC,EAAEjB,CAAC,CAAC;IACpD,IAAIK,CAAC,IAAI,CAACM,KAAK,CAACO,cAAc,CAACb,CAAC,CAAC,EAAE,MAAM,IAAIE,KAAK,CAAC,gBAAgB,GAAGF,CAAC,CAAC;IACxE,OAAO;MAACc,IAAI,EAAEd,CAAC;MAAEU,IAAI,EAAEA;IAAI,CAAC;EAC9B,CAAC,CAAC;AACJ;AAEAP,QAAQ,CAACY,SAAS,GAAGrB,QAAQ,CAACqB,SAAS,GAAG;EACxCC,WAAW,EAAEb,QAAQ;EACrBc,EAAE,EAAE,SAAAA,CAASC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAIpB,CAAC,GAAG,IAAI,CAACA,CAAC;MACVqB,CAAC,GAAGhB,cAAc,CAACc,QAAQ,GAAG,EAAE,EAAEnB,CAAC,CAAC;MACpCC,CAAC;MACDL,CAAC,GAAG,CAAC,CAAC;MACNC,CAAC,GAAGwB,CAAC,CAACtB,MAAM;;IAEhB;IACA,IAAID,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACxB,OAAO,EAAEH,CAAC,GAAGC,CAAC,EAAE,IAAI,CAACI,CAAC,GAAG,CAACkB,QAAQ,GAAGE,CAAC,CAACzB,CAAC,CAAC,EAAEmB,IAAI,MAAMd,CAAC,GAAGqB,GAAG,CAACtB,CAAC,CAACC,CAAC,CAAC,EAAEkB,QAAQ,CAACR,IAAI,CAAC,CAAC,EAAE,OAAOV,CAAC;MAC5F;IACF;;IAEA;IACA;IACA,IAAImB,QAAQ,IAAI,IAAI,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE,MAAM,IAAIjB,KAAK,CAAC,oBAAoB,GAAGiB,QAAQ,CAAC;IACxG,OAAO,EAAExB,CAAC,GAAGC,CAAC,EAAE;MACd,IAAII,CAAC,GAAG,CAACkB,QAAQ,GAAGE,CAAC,CAACzB,CAAC,CAAC,EAAEmB,IAAI,EAAEf,CAAC,CAACC,CAAC,CAAC,GAAGsB,GAAG,CAACvB,CAAC,CAACC,CAAC,CAAC,EAAEkB,QAAQ,CAACR,IAAI,EAAES,QAAQ,CAAC,CAAC,KACrE,IAAIA,QAAQ,IAAI,IAAI,EAAE,KAAKnB,CAAC,IAAID,CAAC,EAAEA,CAAC,CAACC,CAAC,CAAC,GAAGsB,GAAG,CAACvB,CAAC,CAACC,CAAC,CAAC,EAAEkB,QAAQ,CAACR,IAAI,EAAE,IAAI,CAAC;IAC/E;IAEA,OAAO,IAAI;EACb,CAAC;EACDa,IAAI,EAAE,SAAAA,CAAA,EAAW;IACf,IAAIA,IAAI,GAAG,CAAC,CAAC;MAAExB,CAAC,GAAG,IAAI,CAACA,CAAC;IACzB,KAAK,IAAIC,CAAC,IAAID,CAAC,EAAEwB,IAAI,CAACvB,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC;IACvC,OAAO,IAAIT,QAAQ,CAACoB,IAAI,CAAC;EAC3B,CAAC;EACDC,IAAI,EAAE,SAAAA,CAASV,IAAI,EAAEW,IAAI,EAAE;IACzB,IAAI,CAAC7B,CAAC,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI4B,IAAI,GAAG,IAAIC,KAAK,CAAC/B,CAAC,CAAC,EAAED,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEI,CAAC,EAAEL,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE+B,IAAI,CAAC/B,CAAC,CAAC,GAAGE,SAAS,CAACF,CAAC,GAAG,CAAC,CAAC;IACrH,IAAI,CAAC,IAAI,CAACI,CAAC,CAACc,cAAc,CAACC,IAAI,CAAC,EAAE,MAAM,IAAIZ,KAAK,CAAC,gBAAgB,GAAGY,IAAI,CAAC;IAC1E,KAAKd,CAAC,GAAG,IAAI,CAACD,CAAC,CAACe,IAAI,CAAC,EAAEnB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACF,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEK,CAAC,CAACL,CAAC,CAAC,CAACF,KAAK,CAACmC,KAAK,CAACH,IAAI,EAAEC,IAAI,CAAC;EACtF,CAAC;EACDE,KAAK,EAAE,SAAAA,CAASd,IAAI,EAAEW,IAAI,EAAEC,IAAI,EAAE;IAChC,IAAI,CAAC,IAAI,CAAC3B,CAAC,CAACc,cAAc,CAACC,IAAI,CAAC,EAAE,MAAM,IAAIZ,KAAK,CAAC,gBAAgB,GAAGY,IAAI,CAAC;IAC1E,KAAK,IAAId,CAAC,GAAG,IAAI,CAACD,CAAC,CAACe,IAAI,CAAC,EAAEnB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACF,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEK,CAAC,CAACL,CAAC,CAAC,CAACF,KAAK,CAACmC,KAAK,CAACH,IAAI,EAAEC,IAAI,CAAC;EAC1F;AACF,CAAC;AAED,SAASL,GAAGA,CAACP,IAAI,EAAEJ,IAAI,EAAE;EACvB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGkB,IAAI,CAAChB,MAAM,EAAE+B,CAAC,EAAElC,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC9C,IAAI,CAACkC,CAAC,GAAGf,IAAI,CAACnB,CAAC,CAAC,EAAEe,IAAI,KAAKA,IAAI,EAAE;MAC/B,OAAOmB,CAAC,CAACpC,KAAK;IAChB;EACF;AACF;AAEA,SAAS6B,GAAGA,CAACR,IAAI,EAAEJ,IAAI,EAAES,QAAQ,EAAE;EACjC,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGkB,IAAI,CAAChB,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC3C,IAAImB,IAAI,CAACnB,CAAC,CAAC,CAACe,IAAI,KAAKA,IAAI,EAAE;MACzBI,IAAI,CAACnB,CAAC,CAAC,GAAGH,IAAI,EAAEsB,IAAI,GAAGA,IAAI,CAACF,KAAK,CAAC,CAAC,EAAEjB,CAAC,CAAC,CAACmC,MAAM,CAAChB,IAAI,CAACF,KAAK,CAACjB,CAAC,GAAG,CAAC,CAAC,CAAC;MACjE;IACF;EACF;EACA,IAAIwB,QAAQ,IAAI,IAAI,EAAEL,IAAI,CAACiB,IAAI,CAAC;IAACrB,IAAI,EAAEA,IAAI;IAAEjB,KAAK,EAAE0B;EAAQ,CAAC,CAAC;EAC9D,OAAOL,IAAI;AACb;AAEA,eAAepB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import curveLinear from \"./linear.js\";\nexport var curveRadialLinear = curveRadial(curveLinear);\nfunction Radial(curve) {\n  this._curve = curve;\n}\nRadial.prototype = {\n  areaStart: function () {\n    this._curve.areaStart();\n  },\n  areaEnd: function () {\n    this._curve.areaEnd();\n  },\n  lineStart: function () {\n    this._curve.lineStart();\n  },\n  lineEnd: function () {\n    this._curve.lineEnd();\n  },\n  point: function (a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\nexport default function curveRadial(curve) {\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n  radial._curve = curve;\n  return radial;\n}", "map": {"version": 3, "names": ["curveLinear", "curveRadialLinear", "curveRadial", "Radial", "curve", "_curve", "prototype", "areaStart", "areaEnd", "lineStart", "lineEnd", "point", "a", "r", "Math", "sin", "cos", "radial", "context"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-shape/src/curve/radial.js"], "sourcesContent": ["import curveLinear from \"./linear.js\";\n\nexport var curveRadialLinear = curveRadial(curveLinear);\n\nfunction Radial(curve) {\n  this._curve = curve;\n}\n\nRadial.prototype = {\n  areaStart: function() {\n    this._curve.areaStart();\n  },\n  areaEnd: function() {\n    this._curve.areaEnd();\n  },\n  lineStart: function() {\n    this._curve.lineStart();\n  },\n  lineEnd: function() {\n    this._curve.lineEnd();\n  },\n  point: function(a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\n\nexport default function curveRadial(curve) {\n\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n\n  radial._curve = curve;\n\n  return radial;\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,aAAa;AAErC,OAAO,IAAIC,iBAAiB,GAAGC,WAAW,CAACF,WAAW,CAAC;AAEvD,SAASG,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAI,CAACC,MAAM,GAAGD,KAAK;AACrB;AAEAD,MAAM,CAACG,SAAS,GAAG;EACjBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC,CAAC;EACzB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACH,MAAM,CAACG,OAAO,CAAC,CAAC;EACvB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACJ,MAAM,CAACI,SAAS,CAAC,CAAC;EACzB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACL,MAAM,CAACK,OAAO,CAAC,CAAC;EACvB,CAAC;EACDC,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACR,MAAM,CAACM,KAAK,CAACE,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC,EAAEC,CAAC,GAAG,CAACC,IAAI,CAACE,GAAG,CAACJ,CAAC,CAAC,CAAC;EACtD;AACF,CAAC;AAED,eAAe,SAASV,WAAWA,CAACE,KAAK,EAAE;EAEzC,SAASa,MAAMA,CAACC,OAAO,EAAE;IACvB,OAAO,IAAIf,MAAM,CAACC,KAAK,CAACc,OAAO,CAAC,CAAC;EACnC;EAEAD,MAAM,CAACZ,MAAM,GAAGD,KAAK;EAErB,OAAOa,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
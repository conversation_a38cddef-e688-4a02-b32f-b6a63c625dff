{"ast": null, "code": "export default function (specifier) {\n  var n = specifier.length / 6 | 0,\n    colors = new Array(n),\n    i = 0;\n  while (i < n) colors[i] = \"#\" + specifier.slice(i * 6, ++i * 6);\n  return colors;\n}", "map": {"version": 3, "names": ["specifier", "n", "length", "colors", "Array", "i", "slice"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/colors.js"], "sourcesContent": ["export default function(specifier) {\n  var n = specifier.length / 6 | 0, colors = new Array(n), i = 0;\n  while (i < n) colors[i] = \"#\" + specifier.slice(i * 6, ++i * 6);\n  return colors;\n}\n"], "mappings": "AAAA,eAAe,UAASA,SAAS,EAAE;EACjC,IAAIC,CAAC,GAAGD,SAAS,CAACE,MAAM,GAAG,CAAC,GAAG,CAAC;IAAEC,MAAM,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC9D,OAAOA,CAAC,GAAGJ,CAAC,EAAEE,MAAM,CAACE,CAAC,CAAC,GAAG,GAAG,GAAGL,SAAS,CAACM,KAAK,CAACD,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC/D,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
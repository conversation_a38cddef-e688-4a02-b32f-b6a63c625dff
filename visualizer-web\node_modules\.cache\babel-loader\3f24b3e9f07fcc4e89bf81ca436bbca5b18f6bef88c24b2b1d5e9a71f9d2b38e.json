{"ast": null, "code": "var frame = 0,\n  // is an animation frame pending?\n  timeout = 0,\n  // is a timeout pending?\n  interval = 0,\n  // are any timers active?\n  pokeDelay = 1000,\n  // how frequently we check for clock skew\n  taskHead,\n  taskTail,\n  clockLast = 0,\n  clockNow = 0,\n  clockSkew = 0,\n  clock = typeof performance === \"object\" && performance.now ? performance : Date,\n  setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function (f) {\n    setTimeout(f, 17);\n  };\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\nfunction clearNow() {\n  clockNow = 0;\n}\nexport function Timer() {\n  this._call = this._time = this._next = null;\n}\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function (callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function () {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\nexport function timer(callback, delay, time) {\n  var t = new Timer();\n  t.restart(callback, delay, time);\n  return t;\n}\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead,\n    e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\nfunction poke() {\n  var now = clock.now(),\n    delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\nfunction nap() {\n  var t0,\n    t1 = taskHead,\n    t2,\n    time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}", "map": {"version": 3, "names": ["frame", "timeout", "interval", "poke<PERSON><PERSON><PERSON>", "taskHead", "taskTail", "clockLast", "clockNow", "clockSkew", "clock", "performance", "now", "Date", "set<PERSON>rame", "window", "requestAnimationFrame", "bind", "f", "setTimeout", "clearNow", "Timer", "_call", "_time", "_next", "prototype", "timer", "constructor", "restart", "callback", "delay", "time", "TypeError", "sleep", "stop", "Infinity", "t", "timer<PERSON><PERSON><PERSON>", "e", "call", "undefined", "wake", "nap", "poke", "t0", "t1", "t2", "clearTimeout", "clearInterval", "setInterval"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-timer/src/timer.js"], "sourcesContent": ["var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n"], "mappings": "AAAA,IAAIA,KAAK,GAAG,CAAC;EAAE;EACXC,OAAO,GAAG,CAAC;EAAE;EACbC,QAAQ,GAAG,CAAC;EAAE;EACdC,SAAS,GAAG,IAAI;EAAE;EAClBC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,CAAC;EACbC,QAAQ,GAAG,CAAC;EACZC,SAAS,GAAG,CAAC;EACbC,KAAK,GAAG,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACC,GAAG,GAAGD,WAAW,GAAGE,IAAI;EAC/EC,QAAQ,GAAG,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,qBAAqB,GAAGD,MAAM,CAACC,qBAAqB,CAACC,IAAI,CAACF,MAAM,CAAC,GAAG,UAASG,CAAC,EAAE;IAAEC,UAAU,CAACD,CAAC,EAAE,EAAE,CAAC;EAAE,CAAC;AAE1J,OAAO,SAASN,GAAGA,CAAA,EAAG;EACpB,OAAOJ,QAAQ,KAAKM,QAAQ,CAACM,QAAQ,CAAC,EAAEZ,QAAQ,GAAGE,KAAK,CAACE,GAAG,CAAC,CAAC,GAAGH,SAAS,CAAC;AAC7E;AAEA,SAASW,QAAQA,CAAA,EAAG;EAClBZ,QAAQ,GAAG,CAAC;AACd;AAEA,OAAO,SAASa,KAAKA,CAAA,EAAG;EACtB,IAAI,CAACC,KAAK,GACV,IAAI,CAACC,KAAK,GACV,IAAI,CAACC,KAAK,GAAG,IAAI;AACnB;AAEAH,KAAK,CAACI,SAAS,GAAGC,KAAK,CAACD,SAAS,GAAG;EAClCE,WAAW,EAAEN,KAAK;EAClBO,OAAO,EAAE,SAAAA,CAASC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE;IACvC,IAAI,OAAOF,QAAQ,KAAK,UAAU,EAAE,MAAM,IAAIG,SAAS,CAAC,4BAA4B,CAAC;IACrFD,IAAI,GAAG,CAACA,IAAI,IAAI,IAAI,GAAGnB,GAAG,CAAC,CAAC,GAAG,CAACmB,IAAI,KAAKD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,KAAK,CAAC;IACpE,IAAI,CAAC,IAAI,CAACN,KAAK,IAAIlB,QAAQ,KAAK,IAAI,EAAE;MACpC,IAAIA,QAAQ,EAAEA,QAAQ,CAACkB,KAAK,GAAG,IAAI,CAAC,KAC/BnB,QAAQ,GAAG,IAAI;MACpBC,QAAQ,GAAG,IAAI;IACjB;IACA,IAAI,CAACgB,KAAK,GAAGO,QAAQ;IACrB,IAAI,CAACN,KAAK,GAAGQ,IAAI;IACjBE,KAAK,CAAC,CAAC;EACT,CAAC;EACDC,IAAI,EAAE,SAAAA,CAAA,EAAW;IACf,IAAI,IAAI,CAACZ,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,GAAG,IAAI;MACjB,IAAI,CAACC,KAAK,GAAGY,QAAQ;MACrBF,KAAK,CAAC,CAAC;IACT;EACF;AACF,CAAC;AAED,OAAO,SAASP,KAAKA,CAACG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC3C,IAAIK,CAAC,GAAG,IAAIf,KAAK,CAAD,CAAC;EACjBe,CAAC,CAACR,OAAO,CAACC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,CAAC;EAChC,OAAOK,CAAC;AACV;AAEA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3BzB,GAAG,CAAC,CAAC,CAAC,CAAC;EACP,EAAEX,KAAK,CAAC,CAAC;EACT,IAAImC,CAAC,GAAG/B,QAAQ;IAAEiC,CAAC;EACnB,OAAOF,CAAC,EAAE;IACR,IAAI,CAACE,CAAC,GAAG9B,QAAQ,GAAG4B,CAAC,CAACb,KAAK,KAAK,CAAC,EAAEa,CAAC,CAACd,KAAK,CAACiB,IAAI,CAACC,SAAS,EAAEF,CAAC,CAAC;IAC7DF,CAAC,GAAGA,CAAC,CAACZ,KAAK;EACb;EACA,EAAEvB,KAAK;AACT;AAEA,SAASwC,IAAIA,CAAA,EAAG;EACdjC,QAAQ,GAAG,CAACD,SAAS,GAAGG,KAAK,CAACE,GAAG,CAAC,CAAC,IAAIH,SAAS;EAChDR,KAAK,GAAGC,OAAO,GAAG,CAAC;EACnB,IAAI;IACFmC,UAAU,CAAC,CAAC;EACd,CAAC,SAAS;IACRpC,KAAK,GAAG,CAAC;IACTyC,GAAG,CAAC,CAAC;IACLlC,QAAQ,GAAG,CAAC;EACd;AACF;AAEA,SAASmC,IAAIA,CAAA,EAAG;EACd,IAAI/B,GAAG,GAAGF,KAAK,CAACE,GAAG,CAAC,CAAC;IAAEkB,KAAK,GAAGlB,GAAG,GAAGL,SAAS;EAC9C,IAAIuB,KAAK,GAAG1B,SAAS,EAAEK,SAAS,IAAIqB,KAAK,EAAEvB,SAAS,GAAGK,GAAG;AAC5D;AAEA,SAAS8B,GAAGA,CAAA,EAAG;EACb,IAAIE,EAAE;IAAEC,EAAE,GAAGxC,QAAQ;IAAEyC,EAAE;IAAEf,IAAI,GAAGI,QAAQ;EAC1C,OAAOU,EAAE,EAAE;IACT,IAAIA,EAAE,CAACvB,KAAK,EAAE;MACZ,IAAIS,IAAI,GAAGc,EAAE,CAACtB,KAAK,EAAEQ,IAAI,GAAGc,EAAE,CAACtB,KAAK;MACpCqB,EAAE,GAAGC,EAAE,EAAEA,EAAE,GAAGA,EAAE,CAACrB,KAAK;IACxB,CAAC,MAAM;MACLsB,EAAE,GAAGD,EAAE,CAACrB,KAAK,EAAEqB,EAAE,CAACrB,KAAK,GAAG,IAAI;MAC9BqB,EAAE,GAAGD,EAAE,GAAGA,EAAE,CAACpB,KAAK,GAAGsB,EAAE,GAAGzC,QAAQ,GAAGyC,EAAE;IACzC;EACF;EACAxC,QAAQ,GAAGsC,EAAE;EACbX,KAAK,CAACF,IAAI,CAAC;AACb;AAEA,SAASE,KAAKA,CAACF,IAAI,EAAE;EACnB,IAAI9B,KAAK,EAAE,OAAO,CAAC;EACnB,IAAIC,OAAO,EAAEA,OAAO,GAAG6C,YAAY,CAAC7C,OAAO,CAAC;EAC5C,IAAI4B,KAAK,GAAGC,IAAI,GAAGvB,QAAQ,CAAC,CAAC;EAC7B,IAAIsB,KAAK,GAAG,EAAE,EAAE;IACd,IAAIC,IAAI,GAAGI,QAAQ,EAAEjC,OAAO,GAAGiB,UAAU,CAACsB,IAAI,EAAEV,IAAI,GAAGrB,KAAK,CAACE,GAAG,CAAC,CAAC,GAAGH,SAAS,CAAC;IAC/E,IAAIN,QAAQ,EAAEA,QAAQ,GAAG6C,aAAa,CAAC7C,QAAQ,CAAC;EAClD,CAAC,MAAM;IACL,IAAI,CAACA,QAAQ,EAAEI,SAAS,GAAGG,KAAK,CAACE,GAAG,CAAC,CAAC,EAAET,QAAQ,GAAG8C,WAAW,CAACN,IAAI,EAAEvC,SAAS,CAAC;IAC/EH,KAAK,GAAG,CAAC,EAAEa,QAAQ,CAAC2B,IAAI,CAAC;EAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
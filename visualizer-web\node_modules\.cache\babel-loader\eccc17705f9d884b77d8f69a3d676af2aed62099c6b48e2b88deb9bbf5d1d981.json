{"ast": null, "code": "/**\n * Manages a simulation of physical forces acting on bodies and springs.\n */\nmodule.exports = createPhysicsSimulator;\nvar generateCreateBodyFunction = require('./codeGenerators/generateCreateBody');\nvar generateQuadTreeFunction = require('./codeGenerators/generateQuadTree');\nvar generateBoundsFunction = require('./codeGenerators/generateBounds');\nvar generateCreateDragForceFunction = require('./codeGenerators/generateCreateDragForce');\nvar generateCreateSpringForceFunction = require('./codeGenerators/generateCreateSpringForce');\nvar generateIntegratorFunction = require('./codeGenerators/generateIntegrator');\nvar dimensionalCache = {};\nfunction createPhysicsSimulator(settings) {\n  var Spring = require('./spring');\n  var merge = require('ngraph.merge');\n  var eventify = require('ngraph.events');\n  if (settings) {\n    // Check for names from older versions of the layout\n    if (settings.springCoeff !== undefined) throw new Error('springCoeff was renamed to springCoefficient');\n    if (settings.dragCoeff !== undefined) throw new Error('dragCoeff was renamed to dragCoefficient');\n  }\n  settings = merge(settings, {\n    /**\n     * Ideal length for links (springs in physical model).\n     */\n    springLength: 10,\n    /**\n     * Hook's law coefficient. 1 - solid spring.\n     */\n    springCoefficient: 0.8,\n    /**\n     * Coulomb's law coefficient. It's used to repel nodes thus should be negative\n     * if you make it positive nodes start attract each other :).\n     */\n    gravity: -12,\n    /**\n     * Theta coefficient from Barnes Hut simulation. Ranged between (0, 1).\n     * The closer it's to 1 the more nodes algorithm will have to go through.\n     * Setting it to one makes Barnes Hut simulation no different from\n     * brute-force forces calculation (each node is considered).\n     */\n    theta: 0.8,\n    /**\n     * Drag force coefficient. Used to slow down system, thus should be less than 1.\n     * The closer it is to 0 the less tight system will be.\n     */\n    dragCoefficient: 0.9,\n    // TODO: Need to rename this to something better. E.g. `dragCoefficient`\n\n    /**\n     * Default time step (dt) for forces integration\n     */\n    timeStep: 0.5,\n    /**\n     * Adaptive time step uses average spring length to compute actual time step:\n     * See: https://twitter.com/anvaka/status/1293067160755957760\n     */\n    adaptiveTimeStepWeight: 0,\n    /**\n     * This parameter defines number of dimensions of the space where simulation\n     * is performed. \n     */\n    dimensions: 2,\n    /**\n     * In debug mode more checks are performed, this will help you catch errors\n     * quickly, however for production build it is recommended to turn off this flag\n     * to speed up computation.\n     */\n    debug: false\n  });\n  var factory = dimensionalCache[settings.dimensions];\n  if (!factory) {\n    var dimensions = settings.dimensions;\n    factory = {\n      Body: generateCreateBodyFunction(dimensions, settings.debug),\n      createQuadTree: generateQuadTreeFunction(dimensions),\n      createBounds: generateBoundsFunction(dimensions),\n      createDragForce: generateCreateDragForceFunction(dimensions),\n      createSpringForce: generateCreateSpringForceFunction(dimensions),\n      integrate: generateIntegratorFunction(dimensions)\n    };\n    dimensionalCache[dimensions] = factory;\n  }\n  var Body = factory.Body;\n  var createQuadTree = factory.createQuadTree;\n  var createBounds = factory.createBounds;\n  var createDragForce = factory.createDragForce;\n  var createSpringForce = factory.createSpringForce;\n  var integrate = factory.integrate;\n  var createBody = pos => new Body(pos);\n  var random = require('ngraph.random').random(42);\n  var bodies = []; // Bodies in this simulation.\n  var springs = []; // Springs in this simulation.\n\n  var quadTree = createQuadTree(settings, random);\n  var bounds = createBounds(bodies, settings, random);\n  var springForce = createSpringForce(settings, random);\n  var dragForce = createDragForce(settings);\n  var totalMovement = 0; // how much movement we made on last step\n  var forces = [];\n  var forceMap = new Map();\n  var iterationNumber = 0;\n  addForce('nbody', nbodyForce);\n  addForce('spring', updateSpringForce);\n  var publicApi = {\n    /**\n     * Array of bodies, registered with current simulator\n     *\n     * Note: To add new body, use addBody() method. This property is only\n     * exposed for testing/performance purposes.\n     */\n    bodies: bodies,\n    quadTree: quadTree,\n    /**\n     * Array of springs, registered with current simulator\n     *\n     * Note: To add new spring, use addSpring() method. This property is only\n     * exposed for testing/performance purposes.\n     */\n    springs: springs,\n    /**\n     * Returns settings with which current simulator was initialized\n     */\n    settings: settings,\n    /**\n     * Adds a new force to simulation\n     */\n    addForce: addForce,\n    /**\n     * Removes a force from the simulation.\n     */\n    removeForce: removeForce,\n    /**\n     * Returns a map of all registered forces.\n     */\n    getForces: getForces,\n    /**\n     * Performs one step of force simulation.\n     *\n     * @returns {boolean} true if system is considered stable; False otherwise.\n     */\n    step: function () {\n      for (var i = 0; i < forces.length; ++i) {\n        forces[i](iterationNumber);\n      }\n      var movement = integrate(bodies, settings.timeStep, settings.adaptiveTimeStepWeight);\n      iterationNumber += 1;\n      return movement;\n    },\n    /**\n     * Adds body to the system\n     *\n     * @param {ngraph.physics.primitives.Body} body physical body\n     *\n     * @returns {ngraph.physics.primitives.Body} added body\n     */\n    addBody: function (body) {\n      if (!body) {\n        throw new Error('Body is required');\n      }\n      bodies.push(body);\n      return body;\n    },\n    /**\n     * Adds body to the system at given position\n     *\n     * @param {Object} pos position of a body\n     *\n     * @returns {ngraph.physics.primitives.Body} added body\n     */\n    addBodyAt: function (pos) {\n      if (!pos) {\n        throw new Error('Body position is required');\n      }\n      var body = createBody(pos);\n      bodies.push(body);\n      return body;\n    },\n    /**\n     * Removes body from the system\n     *\n     * @param {ngraph.physics.primitives.Body} body to remove\n     *\n     * @returns {Boolean} true if body found and removed. falsy otherwise;\n     */\n    removeBody: function (body) {\n      if (!body) {\n        return;\n      }\n      var idx = bodies.indexOf(body);\n      if (idx < 0) {\n        return;\n      }\n      bodies.splice(idx, 1);\n      if (bodies.length === 0) {\n        bounds.reset();\n      }\n      return true;\n    },\n    /**\n     * Adds a spring to this simulation.\n     *\n     * @returns {Object} - a handle for a spring. If you want to later remove\n     * spring pass it to removeSpring() method.\n     */\n    addSpring: function (body1, body2, springLength, springCoefficient) {\n      if (!body1 || !body2) {\n        throw new Error('Cannot add null spring to force simulator');\n      }\n      if (typeof springLength !== 'number') {\n        springLength = -1; // assume global configuration\n      }\n      var spring = new Spring(body1, body2, springLength, springCoefficient >= 0 ? springCoefficient : -1);\n      springs.push(spring);\n\n      // TODO: could mark simulator as dirty.\n      return spring;\n    },\n    /**\n     * Returns amount of movement performed on last step() call\n     */\n    getTotalMovement: function () {\n      return totalMovement;\n    },\n    /**\n     * Removes spring from the system\n     *\n     * @param {Object} spring to remove. Spring is an object returned by addSpring\n     *\n     * @returns {Boolean} true if spring found and removed. falsy otherwise;\n     */\n    removeSpring: function (spring) {\n      if (!spring) {\n        return;\n      }\n      var idx = springs.indexOf(spring);\n      if (idx > -1) {\n        springs.splice(idx, 1);\n        return true;\n      }\n    },\n    getBestNewBodyPosition: function (neighbors) {\n      return bounds.getBestNewPosition(neighbors);\n    },\n    /**\n     * Returns bounding box which covers all bodies\n     */\n    getBBox: getBoundingBox,\n    getBoundingBox: getBoundingBox,\n    invalidateBBox: function () {\n      console.warn('invalidateBBox() is deprecated, bounds always recomputed on `getBBox()` call');\n    },\n    // TODO: Move the force specific stuff to force\n    gravity: function (value) {\n      if (value !== undefined) {\n        settings.gravity = value;\n        quadTree.options({\n          gravity: value\n        });\n        return this;\n      } else {\n        return settings.gravity;\n      }\n    },\n    theta: function (value) {\n      if (value !== undefined) {\n        settings.theta = value;\n        quadTree.options({\n          theta: value\n        });\n        return this;\n      } else {\n        return settings.theta;\n      }\n    },\n    /**\n     * Returns pseudo-random number generator instance.\n     */\n    random: random\n  };\n\n  // allow settings modification via public API:\n  expose(settings, publicApi);\n  eventify(publicApi);\n  return publicApi;\n  function getBoundingBox() {\n    bounds.update();\n    return bounds.box;\n  }\n  function addForce(forceName, forceFunction) {\n    if (forceMap.has(forceName)) throw new Error('Force ' + forceName + ' is already added');\n    forceMap.set(forceName, forceFunction);\n    forces.push(forceFunction);\n  }\n  function removeForce(forceName) {\n    var forceIndex = forces.indexOf(forceMap.get(forceName));\n    if (forceIndex < 0) return;\n    forces.splice(forceIndex, 1);\n    forceMap.delete(forceName);\n  }\n  function getForces() {\n    // TODO: Should I trust them or clone the forces?\n    return forceMap;\n  }\n  function nbodyForce(/* iterationUmber */\n  ) {\n    if (bodies.length === 0) return;\n    quadTree.insertBodies(bodies);\n    var i = bodies.length;\n    while (i--) {\n      var body = bodies[i];\n      if (!body.isPinned) {\n        body.reset();\n        quadTree.updateBodyForce(body);\n        dragForce.update(body);\n      }\n    }\n  }\n  function updateSpringForce() {\n    var i = springs.length;\n    while (i--) {\n      springForce.update(springs[i]);\n    }\n  }\n}\nfunction expose(settings, target) {\n  for (var key in settings) {\n    augment(settings, target, key);\n  }\n}\nfunction augment(source, target, key) {\n  if (!source.hasOwnProperty(key)) return;\n  if (typeof target[key] === 'function') {\n    // this accessor is already defined. Ignore it\n    return;\n  }\n  var sourceIsNumber = Number.isFinite(source[key]);\n  if (sourceIsNumber) {\n    target[key] = function (value) {\n      if (value !== undefined) {\n        if (!Number.isFinite(value)) throw new Error('Value of ' + key + ' should be a valid number.');\n        source[key] = value;\n        return target;\n      }\n      return source[key];\n    };\n  } else {\n    target[key] = function (value) {\n      if (value !== undefined) {\n        source[key] = value;\n        return target;\n      }\n      return source[key];\n    };\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "createPhysicsSimulator", "generateCreateBodyFunction", "require", "generateQuadTreeFunction", "generateBoundsFunction", "generateCreateDragForceFunction", "generateCreateSpringForceFunction", "generateIntegratorFunction", "dimensionalCache", "settings", "Spring", "merge", "eventify", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Error", "<PERSON><PERSON><PERSON><PERSON>", "spring<PERSON>ength", "springCoefficient", "gravity", "theta", "dragCoefficient", "timeStep", "adaptiveTimeStepWeight", "dimensions", "debug", "factory", "Body", "createQuadTree", "createBounds", "createDragForce", "createSpringForce", "integrate", "createBody", "pos", "random", "bodies", "springs", "quadTree", "bounds", "springForce", "dragForce", "totalMovement", "forces", "forceMap", "Map", "iterationNumber", "addForce", "nbodyForce", "updateSpringForce", "publicApi", "remove<PERSON><PERSON>ce", "getForces", "step", "i", "length", "movement", "addBody", "body", "push", "addBodyAt", "removeBody", "idx", "indexOf", "splice", "reset", "addSpring", "body1", "body2", "spring", "getTotalMovement", "removeSpring", "getBestNewBodyPosition", "neighbors", "getBestNewPosition", "getBBox", "getBoundingBox", "invalidate<PERSON><PERSON>", "console", "warn", "value", "options", "expose", "update", "box", "forceName", "forceFunction", "has", "set", "forceIndex", "get", "delete", "insertBodies", "isPinned", "updateBodyForce", "target", "key", "augment", "source", "hasOwnProperty", "sourceIsNumber", "Number", "isFinite"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/createPhysicsSimulator.js"], "sourcesContent": ["/**\n * Manages a simulation of physical forces acting on bodies and springs.\n */\nmodule.exports = createPhysicsSimulator;\n\nvar generateCreateBodyFunction = require('./codeGenerators/generateCreateBody');\nvar generateQuadTreeFunction = require('./codeGenerators/generateQuadTree');\nvar generateBoundsFunction = require('./codeGenerators/generateBounds');\nvar generateCreateDragForceFunction = require('./codeGenerators/generateCreateDragForce');\nvar generateCreateSpringForceFunction = require('./codeGenerators/generateCreateSpringForce');\nvar generateIntegratorFunction = require('./codeGenerators/generateIntegrator');\n\nvar dimensionalCache = {};\n\nfunction createPhysicsSimulator(settings) {\n  var Spring = require('./spring');\n  var merge = require('ngraph.merge');\n  var eventify = require('ngraph.events');\n  if (settings) {\n    // Check for names from older versions of the layout\n    if (settings.springCoeff !== undefined) throw new Error('springCoeff was renamed to springCoefficient');\n    if (settings.dragCoeff !== undefined) throw new Error('dragCoeff was renamed to dragCoefficient');\n  }\n\n  settings = merge(settings, {\n      /**\n       * Ideal length for links (springs in physical model).\n       */\n      springLength: 10,\n\n      /**\n       * Hook's law coefficient. 1 - solid spring.\n       */\n      springCoefficient: 0.8, \n\n      /**\n       * Coulomb's law coefficient. It's used to repel nodes thus should be negative\n       * if you make it positive nodes start attract each other :).\n       */\n      gravity: -12,\n\n      /**\n       * Theta coefficient from Barnes Hut simulation. Ranged between (0, 1).\n       * The closer it's to 1 the more nodes algorithm will have to go through.\n       * Setting it to one makes Barnes Hut simulation no different from\n       * brute-force forces calculation (each node is considered).\n       */\n      theta: 0.8,\n\n      /**\n       * Drag force coefficient. Used to slow down system, thus should be less than 1.\n       * The closer it is to 0 the less tight system will be.\n       */\n      dragCoefficient: 0.9, // TODO: Need to rename this to something better. E.g. `dragCoefficient`\n\n      /**\n       * Default time step (dt) for forces integration\n       */\n      timeStep : 0.5,\n\n      /**\n       * Adaptive time step uses average spring length to compute actual time step:\n       * See: https://twitter.com/anvaka/status/1293067160755957760\n       */\n      adaptiveTimeStepWeight: 0,\n\n      /**\n       * This parameter defines number of dimensions of the space where simulation\n       * is performed. \n       */\n      dimensions: 2,\n\n      /**\n       * In debug mode more checks are performed, this will help you catch errors\n       * quickly, however for production build it is recommended to turn off this flag\n       * to speed up computation.\n       */\n      debug: false\n  });\n\n  var factory = dimensionalCache[settings.dimensions];\n  if (!factory) {\n    var dimensions = settings.dimensions;\n    factory = {\n      Body: generateCreateBodyFunction(dimensions, settings.debug),\n      createQuadTree: generateQuadTreeFunction(dimensions),\n      createBounds: generateBoundsFunction(dimensions),\n      createDragForce: generateCreateDragForceFunction(dimensions),\n      createSpringForce: generateCreateSpringForceFunction(dimensions),\n      integrate: generateIntegratorFunction(dimensions),\n    };\n    dimensionalCache[dimensions] = factory;\n  }\n\n  var Body = factory.Body;\n  var createQuadTree = factory.createQuadTree;\n  var createBounds = factory.createBounds;\n  var createDragForce = factory.createDragForce;\n  var createSpringForce = factory.createSpringForce;\n  var integrate = factory.integrate;\n  var createBody = pos => new Body(pos);\n\n  var random = require('ngraph.random').random(42);\n  var bodies = []; // Bodies in this simulation.\n  var springs = []; // Springs in this simulation.\n\n  var quadTree = createQuadTree(settings, random);\n  var bounds = createBounds(bodies, settings, random);\n  var springForce = createSpringForce(settings, random);\n  var dragForce = createDragForce(settings);\n\n  var totalMovement = 0; // how much movement we made on last step\n  var forces = [];\n  var forceMap = new Map();\n  var iterationNumber = 0;\n \n  addForce('nbody', nbodyForce);\n  addForce('spring', updateSpringForce);\n\n  var publicApi = {\n    /**\n     * Array of bodies, registered with current simulator\n     *\n     * Note: To add new body, use addBody() method. This property is only\n     * exposed for testing/performance purposes.\n     */\n    bodies: bodies,\n  \n    quadTree: quadTree,\n\n    /**\n     * Array of springs, registered with current simulator\n     *\n     * Note: To add new spring, use addSpring() method. This property is only\n     * exposed for testing/performance purposes.\n     */\n    springs: springs,\n\n    /**\n     * Returns settings with which current simulator was initialized\n     */\n    settings: settings,\n\n    /**\n     * Adds a new force to simulation\n     */\n    addForce: addForce,\n    \n    /**\n     * Removes a force from the simulation.\n     */\n    removeForce: removeForce,\n\n    /**\n     * Returns a map of all registered forces.\n     */\n    getForces: getForces,\n\n    /**\n     * Performs one step of force simulation.\n     *\n     * @returns {boolean} true if system is considered stable; False otherwise.\n     */\n    step: function () {\n      for (var i = 0; i < forces.length; ++i) {\n        forces[i](iterationNumber);\n      }\n      var movement = integrate(bodies, settings.timeStep, settings.adaptiveTimeStepWeight);\n      iterationNumber += 1;\n      return movement;\n    },\n\n    /**\n     * Adds body to the system\n     *\n     * @param {ngraph.physics.primitives.Body} body physical body\n     *\n     * @returns {ngraph.physics.primitives.Body} added body\n     */\n    addBody: function (body) {\n      if (!body) {\n        throw new Error('Body is required');\n      }\n      bodies.push(body);\n\n      return body;\n    },\n\n    /**\n     * Adds body to the system at given position\n     *\n     * @param {Object} pos position of a body\n     *\n     * @returns {ngraph.physics.primitives.Body} added body\n     */\n    addBodyAt: function (pos) {\n      if (!pos) {\n        throw new Error('Body position is required');\n      }\n      var body = createBody(pos);\n      bodies.push(body);\n\n      return body;\n    },\n\n    /**\n     * Removes body from the system\n     *\n     * @param {ngraph.physics.primitives.Body} body to remove\n     *\n     * @returns {Boolean} true if body found and removed. falsy otherwise;\n     */\n    removeBody: function (body) {\n      if (!body) { return; }\n\n      var idx = bodies.indexOf(body);\n      if (idx < 0) { return; }\n\n      bodies.splice(idx, 1);\n      if (bodies.length === 0) {\n        bounds.reset();\n      }\n      return true;\n    },\n\n    /**\n     * Adds a spring to this simulation.\n     *\n     * @returns {Object} - a handle for a spring. If you want to later remove\n     * spring pass it to removeSpring() method.\n     */\n    addSpring: function (body1, body2, springLength, springCoefficient) {\n      if (!body1 || !body2) {\n        throw new Error('Cannot add null spring to force simulator');\n      }\n\n      if (typeof springLength !== 'number') {\n        springLength = -1; // assume global configuration\n      }\n\n      var spring = new Spring(body1, body2, springLength, springCoefficient >= 0 ? springCoefficient : -1);\n      springs.push(spring);\n\n      // TODO: could mark simulator as dirty.\n      return spring;\n    },\n\n    /**\n     * Returns amount of movement performed on last step() call\n     */\n    getTotalMovement: function () {\n      return totalMovement;\n    },\n\n    /**\n     * Removes spring from the system\n     *\n     * @param {Object} spring to remove. Spring is an object returned by addSpring\n     *\n     * @returns {Boolean} true if spring found and removed. falsy otherwise;\n     */\n    removeSpring: function (spring) {\n      if (!spring) { return; }\n      var idx = springs.indexOf(spring);\n      if (idx > -1) {\n        springs.splice(idx, 1);\n        return true;\n      }\n    },\n\n    getBestNewBodyPosition: function (neighbors) {\n      return bounds.getBestNewPosition(neighbors);\n    },\n\n    /**\n     * Returns bounding box which covers all bodies\n     */\n    getBBox: getBoundingBox, \n    getBoundingBox: getBoundingBox, \n\n    invalidateBBox: function () {\n      console.warn('invalidateBBox() is deprecated, bounds always recomputed on `getBBox()` call');\n    },\n\n    // TODO: Move the force specific stuff to force\n    gravity: function (value) {\n      if (value !== undefined) {\n        settings.gravity = value;\n        quadTree.options({gravity: value});\n        return this;\n      } else {\n        return settings.gravity;\n      }\n    },\n\n    theta: function (value) {\n      if (value !== undefined) {\n        settings.theta = value;\n        quadTree.options({theta: value});\n        return this;\n      } else {\n        return settings.theta;\n      }\n    },\n\n    /**\n     * Returns pseudo-random number generator instance.\n     */\n    random: random\n  };\n\n  // allow settings modification via public API:\n  expose(settings, publicApi);\n\n  eventify(publicApi);\n\n  return publicApi;\n\n  function getBoundingBox() {\n    bounds.update();\n    return bounds.box;\n  }\n\n  function addForce(forceName, forceFunction) {\n    if (forceMap.has(forceName)) throw new Error('Force ' + forceName + ' is already added');\n\n    forceMap.set(forceName, forceFunction);\n    forces.push(forceFunction);\n  }\n\n  function removeForce(forceName) {\n    var forceIndex = forces.indexOf(forceMap.get(forceName));\n    if (forceIndex < 0) return;\n    forces.splice(forceIndex, 1);\n    forceMap.delete(forceName);\n  }\n\n  function getForces() {\n    // TODO: Should I trust them or clone the forces?\n    return forceMap;\n  }\n\n  function nbodyForce(/* iterationUmber */) {\n    if (bodies.length === 0) return;\n\n    quadTree.insertBodies(bodies);\n    var i = bodies.length;\n    while (i--) {\n      var body = bodies[i];\n      if (!body.isPinned) {\n        body.reset();\n        quadTree.updateBodyForce(body);\n        dragForce.update(body);\n      }\n    }\n  }\n\n  function updateSpringForce() {\n    var i = springs.length;\n    while (i--) {\n      springForce.update(springs[i]);\n    }\n  }\n\n}\n\nfunction expose(settings, target) {\n  for (var key in settings) {\n    augment(settings, target, key);\n  }\n}\n\nfunction augment(source, target, key) {\n  if (!source.hasOwnProperty(key)) return;\n  if (typeof target[key] === 'function') {\n    // this accessor is already defined. Ignore it\n    return;\n  }\n  var sourceIsNumber = Number.isFinite(source[key]);\n\n  if (sourceIsNumber) {\n    target[key] = function (value) {\n      if (value !== undefined) {\n        if (!Number.isFinite(value)) throw new Error('Value of ' + key + ' should be a valid number.');\n        source[key] = value;\n        return target;\n      }\n      return source[key];\n    };\n  } else {\n    target[key] = function (value) {\n      if (value !== undefined) {\n        source[key] = value;\n        return target;\n      }\n      return source[key];\n    };\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAGC,sBAAsB;AAEvC,IAAIC,0BAA0B,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AAC/E,IAAIC,wBAAwB,GAAGD,OAAO,CAAC,mCAAmC,CAAC;AAC3E,IAAIE,sBAAsB,GAAGF,OAAO,CAAC,iCAAiC,CAAC;AACvE,IAAIG,+BAA+B,GAAGH,OAAO,CAAC,0CAA0C,CAAC;AACzF,IAAII,iCAAiC,GAAGJ,OAAO,CAAC,4CAA4C,CAAC;AAC7F,IAAIK,0BAA0B,GAAGL,OAAO,CAAC,qCAAqC,CAAC;AAE/E,IAAIM,gBAAgB,GAAG,CAAC,CAAC;AAEzB,SAASR,sBAAsBA,CAACS,QAAQ,EAAE;EACxC,IAAIC,MAAM,GAAGR,OAAO,CAAC,UAAU,CAAC;EAChC,IAAIS,KAAK,GAAGT,OAAO,CAAC,cAAc,CAAC;EACnC,IAAIU,QAAQ,GAAGV,OAAO,CAAC,eAAe,CAAC;EACvC,IAAIO,QAAQ,EAAE;IACZ;IACA,IAAIA,QAAQ,CAACI,WAAW,KAAKC,SAAS,EAAE,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;IACvG,IAAIN,QAAQ,CAACO,SAAS,KAAKF,SAAS,EAAE,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;EACnG;EAEAN,QAAQ,GAAGE,KAAK,CAACF,QAAQ,EAAE;IACvB;AACN;AACA;IACMQ,YAAY,EAAE,EAAE;IAEhB;AACN;AACA;IACMC,iBAAiB,EAAE,GAAG;IAEtB;AACN;AACA;AACA;IACMC,OAAO,EAAE,CAAC,EAAE;IAEZ;AACN;AACA;AACA;AACA;AACA;IACMC,KAAK,EAAE,GAAG;IAEV;AACN;AACA;AACA;IACMC,eAAe,EAAE,GAAG;IAAE;;IAEtB;AACN;AACA;IACMC,QAAQ,EAAG,GAAG;IAEd;AACN;AACA;AACA;IACMC,sBAAsB,EAAE,CAAC;IAEzB;AACN;AACA;AACA;IACMC,UAAU,EAAE,CAAC;IAEb;AACN;AACA;AACA;AACA;IACMC,KAAK,EAAE;EACX,CAAC,CAAC;EAEF,IAAIC,OAAO,GAAGlB,gBAAgB,CAACC,QAAQ,CAACe,UAAU,CAAC;EACnD,IAAI,CAACE,OAAO,EAAE;IACZ,IAAIF,UAAU,GAAGf,QAAQ,CAACe,UAAU;IACpCE,OAAO,GAAG;MACRC,IAAI,EAAE1B,0BAA0B,CAACuB,UAAU,EAAEf,QAAQ,CAACgB,KAAK,CAAC;MAC5DG,cAAc,EAAEzB,wBAAwB,CAACqB,UAAU,CAAC;MACpDK,YAAY,EAAEzB,sBAAsB,CAACoB,UAAU,CAAC;MAChDM,eAAe,EAAEzB,+BAA+B,CAACmB,UAAU,CAAC;MAC5DO,iBAAiB,EAAEzB,iCAAiC,CAACkB,UAAU,CAAC;MAChEQ,SAAS,EAAEzB,0BAA0B,CAACiB,UAAU;IAClD,CAAC;IACDhB,gBAAgB,CAACgB,UAAU,CAAC,GAAGE,OAAO;EACxC;EAEA,IAAIC,IAAI,GAAGD,OAAO,CAACC,IAAI;EACvB,IAAIC,cAAc,GAAGF,OAAO,CAACE,cAAc;EAC3C,IAAIC,YAAY,GAAGH,OAAO,CAACG,YAAY;EACvC,IAAIC,eAAe,GAAGJ,OAAO,CAACI,eAAe;EAC7C,IAAIC,iBAAiB,GAAGL,OAAO,CAACK,iBAAiB;EACjD,IAAIC,SAAS,GAAGN,OAAO,CAACM,SAAS;EACjC,IAAIC,UAAU,GAAGC,GAAG,IAAI,IAAIP,IAAI,CAACO,GAAG,CAAC;EAErC,IAAIC,MAAM,GAAGjC,OAAO,CAAC,eAAe,CAAC,CAACiC,MAAM,CAAC,EAAE,CAAC;EAChD,IAAIC,MAAM,GAAG,EAAE,CAAC,CAAC;EACjB,IAAIC,OAAO,GAAG,EAAE,CAAC,CAAC;;EAElB,IAAIC,QAAQ,GAAGV,cAAc,CAACnB,QAAQ,EAAE0B,MAAM,CAAC;EAC/C,IAAII,MAAM,GAAGV,YAAY,CAACO,MAAM,EAAE3B,QAAQ,EAAE0B,MAAM,CAAC;EACnD,IAAIK,WAAW,GAAGT,iBAAiB,CAACtB,QAAQ,EAAE0B,MAAM,CAAC;EACrD,IAAIM,SAAS,GAAGX,eAAe,CAACrB,QAAQ,CAAC;EAEzC,IAAIiC,aAAa,GAAG,CAAC,CAAC,CAAC;EACvB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB,IAAIC,eAAe,GAAG,CAAC;EAEvBC,QAAQ,CAAC,OAAO,EAAEC,UAAU,CAAC;EAC7BD,QAAQ,CAAC,QAAQ,EAAEE,iBAAiB,CAAC;EAErC,IAAIC,SAAS,GAAG;IACd;AACJ;AACA;AACA;AACA;AACA;IACId,MAAM,EAAEA,MAAM;IAEdE,QAAQ,EAAEA,QAAQ;IAElB;AACJ;AACA;AACA;AACA;AACA;IACID,OAAO,EAAEA,OAAO;IAEhB;AACJ;AACA;IACI5B,QAAQ,EAAEA,QAAQ;IAElB;AACJ;AACA;IACIsC,QAAQ,EAAEA,QAAQ;IAElB;AACJ;AACA;IACII,WAAW,EAAEA,WAAW;IAExB;AACJ;AACA;IACIC,SAAS,EAAEA,SAAS;IAEpB;AACJ;AACA;AACA;AACA;IACIC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,MAAM,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;QACtCX,MAAM,CAACW,CAAC,CAAC,CAACR,eAAe,CAAC;MAC5B;MACA,IAAIU,QAAQ,GAAGxB,SAAS,CAACI,MAAM,EAAE3B,QAAQ,CAACa,QAAQ,EAAEb,QAAQ,CAACc,sBAAsB,CAAC;MACpFuB,eAAe,IAAI,CAAC;MACpB,OAAOU,QAAQ;IACjB,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,OAAO,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACvB,IAAI,CAACA,IAAI,EAAE;QACT,MAAM,IAAI3C,KAAK,CAAC,kBAAkB,CAAC;MACrC;MACAqB,MAAM,CAACuB,IAAI,CAACD,IAAI,CAAC;MAEjB,OAAOA,IAAI;IACb,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIE,SAAS,EAAE,SAAAA,CAAU1B,GAAG,EAAE;MACxB,IAAI,CAACA,GAAG,EAAE;QACR,MAAM,IAAInB,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MACA,IAAI2C,IAAI,GAAGzB,UAAU,CAACC,GAAG,CAAC;MAC1BE,MAAM,CAACuB,IAAI,CAACD,IAAI,CAAC;MAEjB,OAAOA,IAAI;IACb,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIG,UAAU,EAAE,SAAAA,CAAUH,IAAI,EAAE;MAC1B,IAAI,CAACA,IAAI,EAAE;QAAE;MAAQ;MAErB,IAAII,GAAG,GAAG1B,MAAM,CAAC2B,OAAO,CAACL,IAAI,CAAC;MAC9B,IAAII,GAAG,GAAG,CAAC,EAAE;QAAE;MAAQ;MAEvB1B,MAAM,CAAC4B,MAAM,CAACF,GAAG,EAAE,CAAC,CAAC;MACrB,IAAI1B,MAAM,CAACmB,MAAM,KAAK,CAAC,EAAE;QACvBhB,MAAM,CAAC0B,KAAK,CAAC,CAAC;MAChB;MACA,OAAO,IAAI;IACb,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIC,SAAS,EAAE,SAAAA,CAAUC,KAAK,EAAEC,KAAK,EAAEnD,YAAY,EAAEC,iBAAiB,EAAE;MAClE,IAAI,CAACiD,KAAK,IAAI,CAACC,KAAK,EAAE;QACpB,MAAM,IAAIrD,KAAK,CAAC,2CAA2C,CAAC;MAC9D;MAEA,IAAI,OAAOE,YAAY,KAAK,QAAQ,EAAE;QACpCA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;MACrB;MAEA,IAAIoD,MAAM,GAAG,IAAI3D,MAAM,CAACyD,KAAK,EAAEC,KAAK,EAAEnD,YAAY,EAAEC,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,GAAG,CAAC,CAAC,CAAC;MACpGmB,OAAO,CAACsB,IAAI,CAACU,MAAM,CAAC;;MAEpB;MACA,OAAOA,MAAM;IACf,CAAC;IAED;AACJ;AACA;IACIC,gBAAgB,EAAE,SAAAA,CAAA,EAAY;MAC5B,OAAO5B,aAAa;IACtB,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACI6B,YAAY,EAAE,SAAAA,CAAUF,MAAM,EAAE;MAC9B,IAAI,CAACA,MAAM,EAAE;QAAE;MAAQ;MACvB,IAAIP,GAAG,GAAGzB,OAAO,CAAC0B,OAAO,CAACM,MAAM,CAAC;MACjC,IAAIP,GAAG,GAAG,CAAC,CAAC,EAAE;QACZzB,OAAO,CAAC2B,MAAM,CAACF,GAAG,EAAE,CAAC,CAAC;QACtB,OAAO,IAAI;MACb;IACF,CAAC;IAEDU,sBAAsB,EAAE,SAAAA,CAAUC,SAAS,EAAE;MAC3C,OAAOlC,MAAM,CAACmC,kBAAkB,CAACD,SAAS,CAAC;IAC7C,CAAC;IAED;AACJ;AACA;IACIE,OAAO,EAAEC,cAAc;IACvBA,cAAc,EAAEA,cAAc;IAE9BC,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC1BC,OAAO,CAACC,IAAI,CAAC,8EAA8E,CAAC;IAC9F,CAAC;IAED;IACA5D,OAAO,EAAE,SAAAA,CAAU6D,KAAK,EAAE;MACxB,IAAIA,KAAK,KAAKlE,SAAS,EAAE;QACvBL,QAAQ,CAACU,OAAO,GAAG6D,KAAK;QACxB1C,QAAQ,CAAC2C,OAAO,CAAC;UAAC9D,OAAO,EAAE6D;QAAK,CAAC,CAAC;QAClC,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOvE,QAAQ,CAACU,OAAO;MACzB;IACF,CAAC;IAEDC,KAAK,EAAE,SAAAA,CAAU4D,KAAK,EAAE;MACtB,IAAIA,KAAK,KAAKlE,SAAS,EAAE;QACvBL,QAAQ,CAACW,KAAK,GAAG4D,KAAK;QACtB1C,QAAQ,CAAC2C,OAAO,CAAC;UAAC7D,KAAK,EAAE4D;QAAK,CAAC,CAAC;QAChC,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOvE,QAAQ,CAACW,KAAK;MACvB;IACF,CAAC;IAED;AACJ;AACA;IACIe,MAAM,EAAEA;EACV,CAAC;;EAED;EACA+C,MAAM,CAACzE,QAAQ,EAAEyC,SAAS,CAAC;EAE3BtC,QAAQ,CAACsC,SAAS,CAAC;EAEnB,OAAOA,SAAS;EAEhB,SAAS0B,cAAcA,CAAA,EAAG;IACxBrC,MAAM,CAAC4C,MAAM,CAAC,CAAC;IACf,OAAO5C,MAAM,CAAC6C,GAAG;EACnB;EAEA,SAASrC,QAAQA,CAACsC,SAAS,EAAEC,aAAa,EAAE;IAC1C,IAAI1C,QAAQ,CAAC2C,GAAG,CAACF,SAAS,CAAC,EAAE,MAAM,IAAItE,KAAK,CAAC,QAAQ,GAAGsE,SAAS,GAAG,mBAAmB,CAAC;IAExFzC,QAAQ,CAAC4C,GAAG,CAACH,SAAS,EAAEC,aAAa,CAAC;IACtC3C,MAAM,CAACgB,IAAI,CAAC2B,aAAa,CAAC;EAC5B;EAEA,SAASnC,WAAWA,CAACkC,SAAS,EAAE;IAC9B,IAAII,UAAU,GAAG9C,MAAM,CAACoB,OAAO,CAACnB,QAAQ,CAAC8C,GAAG,CAACL,SAAS,CAAC,CAAC;IACxD,IAAII,UAAU,GAAG,CAAC,EAAE;IACpB9C,MAAM,CAACqB,MAAM,CAACyB,UAAU,EAAE,CAAC,CAAC;IAC5B7C,QAAQ,CAAC+C,MAAM,CAACN,SAAS,CAAC;EAC5B;EAEA,SAASjC,SAASA,CAAA,EAAG;IACnB;IACA,OAAOR,QAAQ;EACjB;EAEA,SAASI,UAAUA,CAAC;EAAA,EAAsB;IACxC,IAAIZ,MAAM,CAACmB,MAAM,KAAK,CAAC,EAAE;IAEzBjB,QAAQ,CAACsD,YAAY,CAACxD,MAAM,CAAC;IAC7B,IAAIkB,CAAC,GAAGlB,MAAM,CAACmB,MAAM;IACrB,OAAOD,CAAC,EAAE,EAAE;MACV,IAAII,IAAI,GAAGtB,MAAM,CAACkB,CAAC,CAAC;MACpB,IAAI,CAACI,IAAI,CAACmC,QAAQ,EAAE;QAClBnC,IAAI,CAACO,KAAK,CAAC,CAAC;QACZ3B,QAAQ,CAACwD,eAAe,CAACpC,IAAI,CAAC;QAC9BjB,SAAS,CAAC0C,MAAM,CAACzB,IAAI,CAAC;MACxB;IACF;EACF;EAEA,SAAST,iBAAiBA,CAAA,EAAG;IAC3B,IAAIK,CAAC,GAAGjB,OAAO,CAACkB,MAAM;IACtB,OAAOD,CAAC,EAAE,EAAE;MACVd,WAAW,CAAC2C,MAAM,CAAC9C,OAAO,CAACiB,CAAC,CAAC,CAAC;IAChC;EACF;AAEF;AAEA,SAAS4B,MAAMA,CAACzE,QAAQ,EAAEsF,MAAM,EAAE;EAChC,KAAK,IAAIC,GAAG,IAAIvF,QAAQ,EAAE;IACxBwF,OAAO,CAACxF,QAAQ,EAAEsF,MAAM,EAAEC,GAAG,CAAC;EAChC;AACF;AAEA,SAASC,OAAOA,CAACC,MAAM,EAAEH,MAAM,EAAEC,GAAG,EAAE;EACpC,IAAI,CAACE,MAAM,CAACC,cAAc,CAACH,GAAG,CAAC,EAAE;EACjC,IAAI,OAAOD,MAAM,CAACC,GAAG,CAAC,KAAK,UAAU,EAAE;IACrC;IACA;EACF;EACA,IAAII,cAAc,GAAGC,MAAM,CAACC,QAAQ,CAACJ,MAAM,CAACF,GAAG,CAAC,CAAC;EAEjD,IAAII,cAAc,EAAE;IAClBL,MAAM,CAACC,GAAG,CAAC,GAAG,UAAUhB,KAAK,EAAE;MAC7B,IAAIA,KAAK,KAAKlE,SAAS,EAAE;QACvB,IAAI,CAACuF,MAAM,CAACC,QAAQ,CAACtB,KAAK,CAAC,EAAE,MAAM,IAAIjE,KAAK,CAAC,WAAW,GAAGiF,GAAG,GAAG,4BAA4B,CAAC;QAC9FE,MAAM,CAACF,GAAG,CAAC,GAAGhB,KAAK;QACnB,OAAOe,MAAM;MACf;MACA,OAAOG,MAAM,CAACF,GAAG,CAAC;IACpB,CAAC;EACH,CAAC,MAAM;IACLD,MAAM,CAACC,GAAG,CAAC,GAAG,UAAUhB,KAAK,EAAE;MAC7B,IAAIA,KAAK,KAAKlE,SAAS,EAAE;QACvBoF,MAAM,CAACF,GAAG,CAAC,GAAGhB,KAAK;QACnB,OAAOe,MAAM;MACf;MACA,OAAOG,MAAM,CAACF,GAAG,CAAC;IACpB,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
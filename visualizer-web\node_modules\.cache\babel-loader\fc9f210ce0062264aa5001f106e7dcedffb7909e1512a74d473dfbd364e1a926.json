{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"f7fcb9addd8e31a354\", \"ffffccc2e69978c679238443\", \"ffffccc2e69978c67931a354006837\", \"ffffccd9f0a3addd8e78c67931a354006837\", \"ffffccd9f0a3addd8e78c67941ab5d238443005a32\", \"ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32\", \"ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-multi/YlGn.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"f7fcb9addd8e31a354\",\n  \"ffffccc2e69978c679238443\",\n  \"ffffccc2e69978c67931a354006837\",\n  \"ffffccd9f0a3addd8e78c67931a354006837\",\n  \"ffffccd9f0a3addd8e78c67941ab5d238443005a32\",\n  \"ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32\",\n  \"ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
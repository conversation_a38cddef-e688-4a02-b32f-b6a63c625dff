{"ast": null, "code": "import { childMatcher } from \"../matcher.js\";\nvar find = Array.prototype.find;\nfunction childFind(match) {\n  return function () {\n    return find.call(this.children, match);\n  };\n}\nfunction childFirst() {\n  return this.firstElementChild;\n}\nexport default function (match) {\n  return this.select(match == null ? childFirst : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}", "map": {"version": 3, "names": ["child<PERSON><PERSON><PERSON>", "find", "Array", "prototype", "child<PERSON><PERSON>", "match", "call", "children", "<PERSON><PERSON><PERSON><PERSON>", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "select"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selection/selectChild.js"], "sourcesContent": ["import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAE1C,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAS,CAACF,IAAI;AAE/B,SAASG,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO,YAAW;IAChB,OAAOJ,IAAI,CAACK,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAEF,KAAK,CAAC;EACxC,CAAC;AACH;AAEA,SAASG,UAAUA,CAAA,EAAG;EACpB,OAAO,IAAI,CAACC,iBAAiB;AAC/B;AAEA,eAAe,UAASJ,KAAK,EAAE;EAC7B,OAAO,IAAI,CAACK,MAAM,CAACL,KAAK,IAAI,IAAI,GAAGG,UAAU,GACvCJ,SAAS,CAAC,OAAOC,KAAK,KAAK,UAAU,GAAGA,KAAK,GAAGL,YAAY,CAACK,KAAK,CAAC,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
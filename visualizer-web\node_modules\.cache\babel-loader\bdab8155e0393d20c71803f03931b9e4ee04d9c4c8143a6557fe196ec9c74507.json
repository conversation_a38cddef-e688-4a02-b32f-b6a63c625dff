{"ast": null, "code": "export default function (t) {\n  t = Math.max(0, Math.min(1, t));\n  return \"rgb(\" + Math.max(0, Math.min(255, Math.round(-4.54 - t * (35.34 - t * (2381.73 - t * (6402.7 - t * (7024.72 - t * 2710.57))))))) + \", \" + Math.max(0, Math.min(255, Math.round(32.49 + t * (170.73 + t * (52.82 - t * (131.46 - t * (176.58 - t * 67.37))))))) + \", \" + Math.max(0, Math.min(255, Math.round(81.24 + t * (442.36 - t * (2482.43 - t * (6167.24 - t * (6614.94 - t * 2475.67))))))) + \")\";\n}", "map": {"version": 3, "names": ["t", "Math", "max", "min", "round"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-multi/cividis.js"], "sourcesContent": ["export default function(t) {\n  t = Math.max(0, Math.min(1, t));\n  return \"rgb(\"\n      + Math.max(0, Math.min(255, Math.round(-4.54 - t * (35.34 - t * (2381.73 - t * (6402.7 - t * (7024.72 - t * 2710.57))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(32.49 + t * (170.73 + t * (52.82 - t * (131.46 - t * (176.58 - t * 67.37))))))) + \", \"\n      + Math.max(0, Math.min(255, Math.round(81.24 + t * (442.36 - t * (2482.43 - t * (6167.24 - t * (6614.94 - t * 2475.67)))))))\n      + \")\";\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzBA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,CAAC,CAAC,CAAC;EAC/B,OAAO,MAAM,GACPC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEF,IAAI,CAACG,KAAK,CAAC,CAAC,IAAI,GAAGJ,CAAC,IAAI,KAAK,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,IAAI,MAAM,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAC/HC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEF,IAAI,CAACG,KAAK,CAAC,KAAK,GAAGJ,CAAC,IAAI,MAAM,GAAGA,CAAC,IAAI,KAAK,GAAGA,CAAC,IAAI,MAAM,GAAGA,CAAC,IAAI,MAAM,GAAGA,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAC3HC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEF,IAAI,CAACG,KAAK,CAAC,KAAK,GAAGJ,CAAC,IAAI,MAAM,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1H,GAAG;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
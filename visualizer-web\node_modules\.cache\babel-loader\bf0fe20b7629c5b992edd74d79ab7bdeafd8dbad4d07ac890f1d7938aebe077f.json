{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"ffeda0feb24cf03b20\", \"ffffb2fecc5cfd8d3ce31a1c\", \"ffffb2fecc5cfd8d3cf03b20bd0026\", \"ffffb2fed976feb24cfd8d3cf03b20bd0026\", \"ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026\", \"ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026\", \"ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-multi/YlOrRd.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ffeda0feb24cf03b20\",\n  \"ffffb2fecc5cfd8d3ce31a1c\",\n  \"ffffb2fecc5cfd8d3cf03b20bd0026\",\n  \"ffffb2fed976feb24cfd8d3cf03b20bd0026\",\n  \"ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026\",\n  \"ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026\",\n  \"ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
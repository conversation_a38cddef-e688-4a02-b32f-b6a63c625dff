{"ast": null, "code": "import namespace from \"./namespace.js\";\nimport { xhtml } from \"./namespaces.js\";\nfunction creatorInherit(name) {\n  return function () {\n    var document = this.ownerDocument,\n      uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml ? document.createElement(name) : document.createElementNS(uri, name);\n  };\n}\nfunction creatorFixed(fullname) {\n  return function () {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\nexport default function (name) {\n  var fullname = namespace(name);\n  return (fullname.local ? creatorFixed : creatorInherit)(fullname);\n}", "map": {"version": 3, "names": ["namespace", "xhtml", "creator<PERSON><PERSON><PERSON><PERSON>", "name", "document", "ownerDocument", "uri", "namespaceURI", "documentElement", "createElement", "createElementNS", "creatorFixed", "fullname", "space", "local"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/creator.js"], "sourcesContent": ["import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,SAAQC,KAAK,QAAO,iBAAiB;AAErC,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC5B,OAAO,YAAW;IAChB,IAAIC,QAAQ,GAAG,IAAI,CAACC,aAAa;MAC7BC,GAAG,GAAG,IAAI,CAACC,YAAY;IAC3B,OAAOD,GAAG,KAAKL,KAAK,IAAIG,QAAQ,CAACI,eAAe,CAACD,YAAY,KAAKN,KAAK,GACjEG,QAAQ,CAACK,aAAa,CAACN,IAAI,CAAC,GAC5BC,QAAQ,CAACM,eAAe,CAACJ,GAAG,EAAEH,IAAI,CAAC;EAC3C,CAAC;AACH;AAEA,SAASQ,YAAYA,CAACC,QAAQ,EAAE;EAC9B,OAAO,YAAW;IAChB,OAAO,IAAI,CAACP,aAAa,CAACK,eAAe,CAACE,QAAQ,CAACC,KAAK,EAAED,QAAQ,CAACE,KAAK,CAAC;EAC3E,CAAC;AACH;AAEA,eAAe,UAASX,IAAI,EAAE;EAC5B,IAAIS,QAAQ,GAAGZ,SAAS,CAACG,IAAI,CAAC;EAC9B,OAAO,CAACS,QAAQ,CAACE,KAAK,GAChBH,YAAY,GACZT,cAAc,EAAEU,QAAQ,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
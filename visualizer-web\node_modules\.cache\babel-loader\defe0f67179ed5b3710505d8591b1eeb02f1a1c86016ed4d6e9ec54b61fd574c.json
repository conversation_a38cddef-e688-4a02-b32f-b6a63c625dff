{"ast": null, "code": "import { interpolateRgbBasis } from \"d3-interpolate\";\nexport default scheme => interpolateRgbBasis(scheme[scheme.length - 1]);", "map": {"version": 3, "names": ["interpolateRgbBasis", "scheme", "length"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/ramp.js"], "sourcesContent": ["import {interpolateRgbBasis} from \"d3-interpolate\";\n\nexport default scheme => interpolateRgbBasis(scheme[scheme.length - 1]);\n"], "mappings": "AAAA,SAAQA,mBAAmB,QAAO,gBAAgB;AAElD,eAAeC,MAAM,IAAID,mBAAmB,CAACC,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import Octant from \"./octant.js\";\nexport default function (x, y, z, radius) {\n  var data,\n    x0 = this._x0,\n    y0 = this._y0,\n    z0 = this._z0,\n    x1,\n    y1,\n    z1,\n    x2,\n    y2,\n    z2,\n    x3 = this._x1,\n    y3 = this._y1,\n    z3 = this._z1,\n    octs = [],\n    node = this._root,\n    q,\n    i;\n  if (node) octs.push(new Octant(node, x0, y0, z0, x3, y3, z3));\n  if (radius == null) radius = Infinity;else {\n    x0 = x - radius, y0 = y - radius, z0 = z - radius;\n    x3 = x + radius, y3 = y + radius, z3 = z + radius;\n    radius *= radius;\n  }\n  while (q = octs.pop()) {\n    // Stop searching if this octant can’t contain a closer node.\n    if (!(node = q.node) || (x1 = q.x0) > x3 || (y1 = q.y0) > y3 || (z1 = q.z0) > z3 || (x2 = q.x1) < x0 || (y2 = q.y1) < y0 || (z2 = q.z1) < z0) continue;\n\n    // Bisect the current octant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n        ym = (y1 + y2) / 2,\n        zm = (z1 + z2) / 2;\n      octs.push(new Octant(node[7], xm, ym, zm, x2, y2, z2), new Octant(node[6], x1, ym, zm, xm, y2, z2), new Octant(node[5], xm, y1, zm, x2, ym, z2), new Octant(node[4], x1, y1, zm, xm, ym, z2), new Octant(node[3], xm, ym, z1, x2, y2, zm), new Octant(node[2], x1, ym, z1, xm, y2, zm), new Octant(node[1], xm, y1, z1, x2, ym, zm), new Octant(node[0], x1, y1, z1, xm, ym, zm));\n\n      // Visit the closest octant first.\n      if (i = (z >= zm) << 2 | (y >= ym) << 1 | x >= xm) {\n        q = octs[octs.length - 1];\n        octs[octs.length - 1] = octs[octs.length - 1 - i];\n        octs[octs.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n        dy = y - +this._y.call(null, node.data),\n        dz = z - +this._z.call(null, node.data),\n        d2 = dx * dx + dy * dy + dz * dz;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d, z0 = z - d;\n        x3 = x + d, y3 = y + d, z3 = z + d;\n        data = node.data;\n      }\n    }\n  }\n  return data;\n}", "map": {"version": 3, "names": ["Octant", "x", "y", "z", "radius", "data", "x0", "_x0", "y0", "_y0", "z0", "_z0", "x1", "y1", "z1", "x2", "y2", "z2", "x3", "_x1", "y3", "_y1", "z3", "_z1", "octs", "node", "_root", "q", "i", "push", "Infinity", "pop", "length", "xm", "ym", "zm", "dx", "_x", "call", "dy", "_y", "dz", "_z", "d2", "d", "Math", "sqrt"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/find.js"], "sourcesContent": ["import Octant from \"./octant.js\";\n\nexport default function(x, y, z, radius) {\n  var data,\n      x0 = this._x0,\n      y0 = this._y0,\n      z0 = this._z0,\n      x1,\n      y1,\n      z1,\n      x2,\n      y2,\n      z2,\n      x3 = this._x1,\n      y3 = this._y1,\n      z3 = this._z1,\n      octs = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) octs.push(new Octant(node, x0, y0, z0, x3, y3, z3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius, y0 = y - radius, z0 = z - radius;\n    x3 = x + radius, y3 = y + radius, z3 = z + radius;\n    radius *= radius;\n  }\n\n  while (q = octs.pop()) {\n\n    // Stop searching if this octant can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (y1 = q.y0) > y3\n        || (z1 = q.z0) > z3\n        || (x2 = q.x1) < x0\n        || (y2 = q.y1) < y0\n        || (z2 = q.z1) < z0) continue;\n\n    // Bisect the current octant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n          ym = (y1 + y2) / 2,\n          zm = (z1 + z2) / 2;\n\n      octs.push(\n        new Octant(node[7], xm, ym, zm, x2, y2, z2),\n        new Octant(node[6], x1, ym, zm, xm, y2, z2),\n        new Octant(node[5], xm, y1, zm, x2, ym, z2),\n        new Octant(node[4], x1, y1, zm, xm, ym, z2),\n        new Octant(node[3], xm, ym, z1, x2, y2, zm),\n        new Octant(node[2], x1, ym, z1, xm, y2, zm),\n        new Octant(node[1], xm, y1, z1, x2, ym, zm),\n        new Octant(node[0], x1, y1, z1, xm, ym, zm)\n      );\n\n      // Visit the closest octant first.\n      if (i = (z >= zm) << 2 | (y >= ym) << 1 | (x >= xm)) {\n        q = octs[octs.length - 1];\n        octs[octs.length - 1] = octs[octs.length - 1 - i];\n        octs[octs.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n          dy = y - +this._y.call(null, node.data),\n          dz = z - +this._z.call(null, node.data),\n          d2 = dx * dx + dy * dy + dz * dz;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d, z0 = z - d;\n        x3 = x + d, y3 = y + d, z3 = z + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAEhC,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE;EACvC,IAAIC,IAAI;IACJC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,IAAI,GAAG,EAAE;IACTC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,CAAC;IACDC,CAAC;EAEL,IAAIH,IAAI,EAAED,IAAI,CAACK,IAAI,CAAC,IAAI7B,MAAM,CAACyB,IAAI,EAAEnB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEQ,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC,CAAC;EAC7D,IAAIlB,MAAM,IAAI,IAAI,EAAEA,MAAM,GAAG0B,QAAQ,CAAC,KACjC;IACHxB,EAAE,GAAGL,CAAC,GAAGG,MAAM,EAAEI,EAAE,GAAGN,CAAC,GAAGE,MAAM,EAAEM,EAAE,GAAGP,CAAC,GAAGC,MAAM;IACjDc,EAAE,GAAGjB,CAAC,GAAGG,MAAM,EAAEgB,EAAE,GAAGlB,CAAC,GAAGE,MAAM,EAAEkB,EAAE,GAAGnB,CAAC,GAAGC,MAAM;IACjDA,MAAM,IAAIA,MAAM;EAClB;EAEA,OAAOuB,CAAC,GAAGH,IAAI,CAACO,GAAG,CAAC,CAAC,EAAE;IAErB;IACA,IAAI,EAAEN,IAAI,GAAGE,CAAC,CAACF,IAAI,CAAC,IACb,CAACb,EAAE,GAAGe,CAAC,CAACrB,EAAE,IAAIY,EAAE,IAChB,CAACL,EAAE,GAAGc,CAAC,CAACnB,EAAE,IAAIY,EAAE,IAChB,CAACN,EAAE,GAAGa,CAAC,CAACjB,EAAE,IAAIY,EAAE,IAChB,CAACP,EAAE,GAAGY,CAAC,CAACf,EAAE,IAAIN,EAAE,IAChB,CAACU,EAAE,GAAGW,CAAC,CAACd,EAAE,IAAIL,EAAE,IAChB,CAACS,EAAE,GAAGU,CAAC,CAACb,EAAE,IAAIJ,EAAE,EAAE;;IAEzB;IACA,IAAIe,IAAI,CAACO,MAAM,EAAE;MACf,IAAIC,EAAE,GAAG,CAACrB,EAAE,GAAGG,EAAE,IAAI,CAAC;QAClBmB,EAAE,GAAG,CAACrB,EAAE,GAAGG,EAAE,IAAI,CAAC;QAClBmB,EAAE,GAAG,CAACrB,EAAE,GAAGG,EAAE,IAAI,CAAC;MAEtBO,IAAI,CAACK,IAAI,CACP,IAAI7B,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEpB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAC3C,IAAIjB,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEb,EAAE,EAAEsB,EAAE,EAAEC,EAAE,EAAEF,EAAE,EAAEjB,EAAE,EAAEC,EAAE,CAAC,EAC3C,IAAIjB,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEpB,EAAE,EAAEsB,EAAE,EAAEpB,EAAE,EAAEmB,EAAE,EAAEjB,EAAE,CAAC,EAC3C,IAAIjB,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEb,EAAE,EAAEC,EAAE,EAAEsB,EAAE,EAAEF,EAAE,EAAEC,EAAE,EAAEjB,EAAE,CAAC,EAC3C,IAAIjB,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEC,EAAE,EAAEpB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEmB,EAAE,CAAC,EAC3C,IAAInC,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEb,EAAE,EAAEsB,EAAE,EAAEpB,EAAE,EAAEmB,EAAE,EAAEjB,EAAE,EAAEmB,EAAE,CAAC,EAC3C,IAAInC,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEpB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEmB,EAAE,EAAEC,EAAE,CAAC,EAC3C,IAAInC,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEmB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAC5C,CAAC;;MAED;MACA,IAAIP,CAAC,GAAG,CAACzB,CAAC,IAAIgC,EAAE,KAAK,CAAC,GAAG,CAACjC,CAAC,IAAIgC,EAAE,KAAK,CAAC,GAAIjC,CAAC,IAAIgC,EAAG,EAAE;QACnDN,CAAC,GAAGH,IAAI,CAACA,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC;QACzBR,IAAI,CAACA,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC,GAAGR,IAAI,CAACA,IAAI,CAACQ,MAAM,GAAG,CAAC,GAAGJ,CAAC,CAAC;QACjDJ,IAAI,CAACA,IAAI,CAACQ,MAAM,GAAG,CAAC,GAAGJ,CAAC,CAAC,GAAGD,CAAC;MAC/B;IACF;;IAEA;IAAA,KACK;MACH,IAAIS,EAAE,GAAGnC,CAAC,GAAG,CAAC,IAAI,CAACoC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEb,IAAI,CAACpB,IAAI,CAAC;QACvCkC,EAAE,GAAGrC,CAAC,GAAG,CAAC,IAAI,CAACsC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEb,IAAI,CAACpB,IAAI,CAAC;QACvCoC,EAAE,GAAGtC,CAAC,GAAG,CAAC,IAAI,CAACuC,EAAE,CAACJ,IAAI,CAAC,IAAI,EAAEb,IAAI,CAACpB,IAAI,CAAC;QACvCsC,EAAE,GAAGP,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE;MACpC,IAAIE,EAAE,GAAGvC,MAAM,EAAE;QACf,IAAIwC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC1C,MAAM,GAAGuC,EAAE,CAAC;QAC9BrC,EAAE,GAAGL,CAAC,GAAG2C,CAAC,EAAEpC,EAAE,GAAGN,CAAC,GAAG0C,CAAC,EAAElC,EAAE,GAAGP,CAAC,GAAGyC,CAAC;QAClC1B,EAAE,GAAGjB,CAAC,GAAG2C,CAAC,EAAExB,EAAE,GAAGlB,CAAC,GAAG0C,CAAC,EAAEtB,EAAE,GAAGnB,CAAC,GAAGyC,CAAC;QAClCvC,IAAI,GAAGoB,IAAI,CAACpB,IAAI;MAClB;IACF;EACF;EAEA,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const createPatternBuilder = require('./createPatternBuilder');\nmodule.exports = generateCreateSpringForceFunction;\nmodule.exports.generateCreateSpringForceFunctionBody = generateCreateSpringForceFunctionBody;\nfunction generateCreateSpringForceFunction(dimension) {\n  let code = generateCreateSpringForceFunctionBody(dimension);\n  return new Function('options', 'random', code);\n}\nfunction generateCreateSpringForceFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  if (!Number.isFinite(options.springCoefficient)) throw new Error('Spring coefficient is not a number');\n  if (!Number.isFinite(options.springLength)) throw new Error('Spring length is not a number');\n\n  return {\n    /**\n     * Updates forces acting on a spring\n     */\n    update: function (spring) {\n      var body1 = spring.from;\n      var body2 = spring.to;\n      var length = spring.length < 0 ? options.springLength : spring.length;\n      ${pattern('var d{var} = body2.pos.{var} - body1.pos.{var};', {\n    indent: 6\n  })}\n      var r = Math.sqrt(${pattern('d{var} * d{var}', {\n    join: ' + '\n  })});\n\n      if (r === 0) {\n        ${pattern('d{var} = (random.nextDouble() - 0.5) / 50;', {\n    indent: 8\n  })}\n        r = Math.sqrt(${pattern('d{var} * d{var}', {\n    join: ' + '\n  })});\n      }\n\n      var d = r - length;\n      var coefficient = ((spring.coefficient > 0) ? spring.coefficient : options.springCoefficient) * d / r;\n\n      ${pattern('body1.force.{var} += coefficient * d{var}', {\n    indent: 6\n  })};\n      body1.springCount += 1;\n      body1.springLength += r;\n\n      ${pattern('body2.force.{var} -= coefficient * d{var}', {\n    indent: 6\n  })};\n      body2.springCount += 1;\n      body2.springLength += r;\n    }\n  };\n`;\n  return code;\n}", "map": {"version": 3, "names": ["createPatternBuilder", "require", "module", "exports", "generateCreateSpringForceFunction", "generateCreateSpringForceFunctionBody", "dimension", "code", "Function", "pattern", "indent", "join"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/generateCreateSpringForce.js"], "sourcesContent": ["const createPatternBuilder = require('./createPatternBuilder');\n\nmodule.exports = generateCreateSpringForceFunction;\nmodule.exports.generateCreateSpringForceFunctionBody = generateCreateSpringForceFunctionBody;\n\nfunction generateCreateSpringForceFunction(dimension) {\n  let code = generateCreateSpringForceFunctionBody(dimension);\n  return new Function('options', 'random', code);\n}\n\nfunction generateCreateSpringForceFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let code = `\n  if (!Number.isFinite(options.springCoefficient)) throw new Error('Spring coefficient is not a number');\n  if (!Number.isFinite(options.springLength)) throw new Error('Spring length is not a number');\n\n  return {\n    /**\n     * Updates forces acting on a spring\n     */\n    update: function (spring) {\n      var body1 = spring.from;\n      var body2 = spring.to;\n      var length = spring.length < 0 ? options.springLength : spring.length;\n      ${pattern('var d{var} = body2.pos.{var} - body1.pos.{var};', {indent: 6})}\n      var r = Math.sqrt(${pattern('d{var} * d{var}', {join: ' + '})});\n\n      if (r === 0) {\n        ${pattern('d{var} = (random.nextDouble() - 0.5) / 50;', {indent: 8})}\n        r = Math.sqrt(${pattern('d{var} * d{var}', {join: ' + '})});\n      }\n\n      var d = r - length;\n      var coefficient = ((spring.coefficient > 0) ? spring.coefficient : options.springCoefficient) * d / r;\n\n      ${pattern('body1.force.{var} += coefficient * d{var}', {indent: 6})};\n      body1.springCount += 1;\n      body1.springLength += r;\n\n      ${pattern('body2.force.{var} -= coefficient * d{var}', {indent: 6})};\n      body2.springCount += 1;\n      body2.springLength += r;\n    }\n  };\n`;\n  return code;\n}\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9DC,MAAM,CAACC,OAAO,GAAGC,iCAAiC;AAClDF,MAAM,CAACC,OAAO,CAACE,qCAAqC,GAAGA,qCAAqC;AAE5F,SAASD,iCAAiCA,CAACE,SAAS,EAAE;EACpD,IAAIC,IAAI,GAAGF,qCAAqC,CAACC,SAAS,CAAC;EAC3D,OAAO,IAAIE,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAED,IAAI,CAAC;AAChD;AAEA,SAASF,qCAAqCA,CAACC,SAAS,EAAE;EACxD,IAAIG,OAAO,GAAGT,oBAAoB,CAACM,SAAS,CAAC;EAC7C,IAAIC,IAAI,GAAG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQE,OAAO,CAAC,iDAAiD,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/E,0BAA0BD,OAAO,CAAC,iBAAiB,EAAE;IAACE,IAAI,EAAE;EAAK,CAAC,CAAC;AACnE;AACA;AACA,UAAUF,OAAO,CAAC,4CAA4C,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AAC5E,wBAAwBD,OAAO,CAAC,iBAAiB,EAAE;IAACE,IAAI,EAAE;EAAK,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA,QAAQF,OAAO,CAAC,2CAA2C,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA,QAAQD,OAAO,CAAC,2CAA2C,EAAE;IAACC,MAAM,EAAE;EAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA,CAAC;EACC,OAAOH,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "module.exports = Spring;\n\n/**\n * Represents a physical spring. Spring connects two bodies, has rest length\n * stiffness coefficient and optional weight\n */\nfunction Spring(fromBody, toBody, length, springCoefficient) {\n  this.from = fromBody;\n  this.to = toBody;\n  this.length = length;\n  this.coefficient = springCoefficient;\n}", "map": {"version": 3, "names": ["module", "exports", "Spring", "fromBody", "toBody", "length", "springCoefficient", "from", "to", "coefficient"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/spring.js"], "sourcesContent": ["module.exports = Spring;\n\n/**\n * Represents a physical spring. Spring connects two bodies, has rest length\n * stiffness coefficient and optional weight\n */\nfunction Spring(fromBody, toBody, length, springCoefficient) {\n    this.from = fromBody;\n    this.to = toBody;\n    this.length = length;\n    this.coefficient = springCoefficient;\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,MAAM;;AAEvB;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,iBAAiB,EAAE;EACzD,IAAI,CAACC,IAAI,GAAGJ,QAAQ;EACpB,IAAI,CAACK,EAAE,GAAGJ,MAAM;EAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACpB,IAAI,CAACI,WAAW,GAAGH,iBAAiB;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
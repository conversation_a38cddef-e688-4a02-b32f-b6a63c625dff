{"ast": null, "code": "export function defaultZ(d) {\n  return d[2];\n}\nexport default function (_) {\n  return arguments.length ? (this._z = _, this) : this._z;\n}", "map": {"version": 3, "names": ["defaultZ", "d", "_", "arguments", "length", "_z"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/z.js"], "sourcesContent": ["export function defaultZ(d) {\n  return d[2];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._z = _, this) : this._z;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,eAAe,UAASC,CAAC,EAAE;EACzB,OAAOC,SAAS,CAACC,MAAM,IAAI,IAAI,CAACC,EAAE,GAAGH,CAAC,EAAE,IAAI,IAAI,IAAI,CAACG,EAAE;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import Octant from \"./octant.js\";\nexport default function (callback) {\n  var octs = [],\n    q,\n    node = this._root,\n    child,\n    x0,\n    y0,\n    z0,\n    x1,\n    y1,\n    z1;\n  if (node) octs.push(new Octant(node, this._x0, this._y0, this._z0, this._x1, this._y1, this._z1));\n  while (q = octs.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, z0 = q.z0, x1 = q.x1, y1 = q.y1, z1 = q.z1) && node.length) {\n      var xm = (x0 + x1) / 2,\n        ym = (y0 + y1) / 2,\n        zm = (z0 + z1) / 2;\n      if (child = node[7]) octs.push(new Octant(child, xm, ym, zm, x1, y1, z1));\n      if (child = node[6]) octs.push(new Octant(child, x0, ym, zm, xm, y1, z1));\n      if (child = node[5]) octs.push(new Octant(child, xm, y0, zm, x1, ym, z1));\n      if (child = node[4]) octs.push(new Octant(child, x0, y0, zm, xm, ym, z1));\n      if (child = node[3]) octs.push(new Octant(child, xm, ym, z0, x1, y1, zm));\n      if (child = node[2]) octs.push(new Octant(child, x0, ym, z0, xm, y1, zm));\n      if (child = node[1]) octs.push(new Octant(child, xm, y0, z0, x1, ym, zm));\n      if (child = node[0]) octs.push(new Octant(child, x0, y0, z0, xm, ym, zm));\n    }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Octant", "callback", "octs", "q", "node", "_root", "child", "x0", "y0", "z0", "x1", "y1", "z1", "push", "_x0", "_y0", "_z0", "_x1", "_y1", "_z1", "pop", "length", "xm", "ym", "zm"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/visit.js"], "sourcesContent": ["import Octant from \"./octant.js\";\n\nexport default function(callback) {\n  var octs = [], q, node = this._root, child, x0, y0, z0, x1, y1, z1;\n  if (node) octs.push(new Octant(node, this._x0, this._y0, this._z0, this._x1, this._y1, this._z1));\n  while (q = octs.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, z0 = q.z0, x1 = q.x1, y1 = q.y1, z1 = q.z1) && node.length) {\n      var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2, zm = (z0 + z1) / 2;\n      if (child = node[7]) octs.push(new Octant(child, xm, ym, zm, x1, y1, z1));\n      if (child = node[6]) octs.push(new Octant(child, x0, ym, zm, xm, y1, z1));\n      if (child = node[5]) octs.push(new Octant(child, xm, y0, zm, x1, ym, z1));\n      if (child = node[4]) octs.push(new Octant(child, x0, y0, zm, xm, ym, z1));\n      if (child = node[3]) octs.push(new Octant(child, xm, ym, z0, x1, y1, zm));\n      if (child = node[2]) octs.push(new Octant(child, x0, ym, z0, xm, y1, zm));\n      if (child = node[1]) octs.push(new Octant(child, xm, y0, z0, x1, ym, zm));\n      if (child = node[0]) octs.push(new Octant(child, x0, y0, z0, xm, ym, zm));\n    }\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAEhC,eAAe,UAASC,QAAQ,EAAE;EAChC,IAAIC,IAAI,GAAG,EAAE;IAAEC,CAAC;IAAEC,IAAI,GAAG,IAAI,CAACC,KAAK;IAAEC,KAAK;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;EAClE,IAAIR,IAAI,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACI,IAAI,EAAE,IAAI,CAACU,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;EACjG,OAAOhB,CAAC,GAAGD,IAAI,CAACkB,GAAG,CAAC,CAAC,EAAE;IACrB,IAAI,CAACnB,QAAQ,CAACG,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAEG,EAAE,GAAGJ,CAAC,CAACI,EAAE,EAAEC,EAAE,GAAGL,CAAC,CAACK,EAAE,EAAEC,EAAE,GAAGN,CAAC,CAACM,EAAE,EAAEC,EAAE,GAAGP,CAAC,CAACO,EAAE,EAAEC,EAAE,GAAGR,CAAC,CAACQ,EAAE,EAAEC,EAAE,GAAGT,CAAC,CAACS,EAAE,CAAC,IAAIR,IAAI,CAACiB,MAAM,EAAE;MAC7G,IAAIC,EAAE,GAAG,CAACf,EAAE,GAAGG,EAAE,IAAI,CAAC;QAAEa,EAAE,GAAG,CAACf,EAAE,GAAGG,EAAE,IAAI,CAAC;QAAEa,EAAE,GAAG,CAACf,EAAE,GAAGG,EAAE,IAAI,CAAC;MAC9D,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEgB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEd,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEC,EAAE,EAAEgB,EAAE,EAAEC,EAAE,EAAEF,EAAE,EAAEX,EAAE,EAAEC,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEgB,EAAE,EAAEd,EAAE,EAAEgB,EAAE,EAAEd,EAAE,EAAEa,EAAE,EAAEX,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEgB,EAAE,EAAEF,EAAE,EAAEC,EAAE,EAAEX,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEgB,EAAE,EAAEC,EAAE,EAAEd,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEa,EAAE,CAAC,CAAC;MACzE,IAAIlB,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEC,EAAE,EAAEgB,EAAE,EAAEd,EAAE,EAAEa,EAAE,EAAEX,EAAE,EAAEa,EAAE,CAAC,CAAC;MACzE,IAAIlB,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEgB,EAAE,EAAEd,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEa,EAAE,EAAEC,EAAE,CAAC,CAAC;MACzE,IAAIlB,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACW,IAAI,CAAC,IAAIb,MAAM,CAACM,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEa,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;IAC3E;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
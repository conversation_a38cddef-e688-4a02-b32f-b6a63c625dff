{"ast": null, "code": "const getVariableName = require('./getVariableName');\nmodule.exports = function createPatternBuilder(dimension) {\n  return pattern;\n  function pattern(template, config) {\n    let indent = config && config.indent || 0;\n    let join = config && config.join !== undefined ? config.join : '\\n';\n    let indentString = Array(indent + 1).join(' ');\n    let buffer = [];\n    for (let i = 0; i < dimension; ++i) {\n      let variableName = getVariableName(i);\n      let prefix = i === 0 ? '' : indentString;\n      buffer.push(prefix + template.replace(/{var}/g, variableName));\n    }\n    return buffer.join(join);\n  }\n};", "map": {"version": 3, "names": ["getVariableName", "require", "module", "exports", "createPatternBuilder", "dimension", "pattern", "template", "config", "indent", "join", "undefined", "indentString", "Array", "buffer", "i", "variableName", "prefix", "push", "replace"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/createPatternBuilder.js"], "sourcesContent": ["const getVariableName = require('./getVariableName');\n\nmodule.exports = function createPatternBuilder(dimension) {\n\n  return pattern;\n  \n  function pattern(template, config) {\n    let indent = (config && config.indent) || 0;\n    let join = (config && config.join !== undefined) ? config.join : '\\n';\n    let indentString = Array(indent + 1).join(' ');\n    let buffer = [];\n    for (let i = 0; i < dimension; ++i) {\n      let variableName = getVariableName(i);\n      let prefix = (i === 0) ? '' : indentString;\n      buffer.push(prefix + template.replace(/{var}/g, variableName));\n    }\n    return buffer.join(join);\n  }\n};\n"], "mappings": "AAAA,MAAMA,eAAe,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAEpDC,MAAM,CAACC,OAAO,GAAG,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EAExD,OAAOC,OAAO;EAEd,SAASA,OAAOA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACjC,IAAIC,MAAM,GAAID,MAAM,IAAIA,MAAM,CAACC,MAAM,IAAK,CAAC;IAC3C,IAAIC,IAAI,GAAIF,MAAM,IAAIA,MAAM,CAACE,IAAI,KAAKC,SAAS,GAAIH,MAAM,CAACE,IAAI,GAAG,IAAI;IACrE,IAAIE,YAAY,GAAGC,KAAK,CAACJ,MAAM,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAC9C,IAAII,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,SAAS,EAAE,EAAEU,CAAC,EAAE;MAClC,IAAIC,YAAY,GAAGhB,eAAe,CAACe,CAAC,CAAC;MACrC,IAAIE,MAAM,GAAIF,CAAC,KAAK,CAAC,GAAI,EAAE,GAAGH,YAAY;MAC1CE,MAAM,CAACI,IAAI,CAACD,MAAM,GAAGV,QAAQ,CAACY,OAAO,CAAC,QAAQ,EAAEH,YAAY,CAAC,CAAC;IAChE;IACA,OAAOF,MAAM,CAACJ,IAAI,CAACA,IAAI,CAAC;EAC1B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
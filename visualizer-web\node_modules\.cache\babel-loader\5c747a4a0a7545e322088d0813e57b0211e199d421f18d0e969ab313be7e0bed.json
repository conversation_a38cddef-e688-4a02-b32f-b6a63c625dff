{"ast": null, "code": "var n,\n  l,\n  u,\n  t,\n  i,\n  r,\n  o,\n  e,\n  f,\n  c,\n  s,\n  a,\n  h,\n  p = {},\n  y = [],\n  v = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,\n  w = Array.isArray;\nfunction d(n, l) {\n  for (var u in l) n[u] = l[u];\n  return n;\n}\nfunction g(n) {\n  n && n.parentNode && n.parentNode.removeChild(n);\n}\nfunction _(l, u, t) {\n  var i,\n    r,\n    o,\n    e = {};\n  for (o in u) \"key\" == o ? i = u[o] : \"ref\" == o ? r = u[o] : e[o] = u[o];\n  if (arguments.length > 2 && (e.children = arguments.length > 3 ? n.call(arguments, 2) : t), \"function\" == typeof l && null != l.defaultProps) for (o in l.defaultProps) null == e[o] && (e[o] = l.defaultProps[o]);\n  return m(l, e, i, r, null);\n}\nfunction m(n, t, i, r, o) {\n  var e = {\n    type: n,\n    props: t,\n    key: i,\n    ref: r,\n    __k: null,\n    __: null,\n    __b: 0,\n    __e: null,\n    __c: null,\n    constructor: void 0,\n    __v: null == o ? ++u : o,\n    __i: -1,\n    __u: 0\n  };\n  return null == o && null != l.vnode && l.vnode(e), e;\n}\nfunction b() {\n  return {\n    current: null\n  };\n}\nfunction k(n) {\n  return n.children;\n}\nfunction x(n, l) {\n  this.props = n, this.context = l;\n}\nfunction S(n, l) {\n  if (null == l) return n.__ ? S(n.__, n.__i + 1) : null;\n  for (var u; l < n.__k.length; l++) if (null != (u = n.__k[l]) && null != u.__e) return u.__e;\n  return \"function\" == typeof n.type ? S(n) : null;\n}\nfunction C(n) {\n  var l, u;\n  if (null != (n = n.__) && null != n.__c) {\n    for (n.__e = n.__c.base = null, l = 0; l < n.__k.length; l++) if (null != (u = n.__k[l]) && null != u.__e) {\n      n.__e = n.__c.base = u.__e;\n      break;\n    }\n    return C(n);\n  }\n}\nfunction M(n) {\n  (!n.__d && (n.__d = !0) && i.push(n) && !$.__r++ || r != l.debounceRendering) && ((r = l.debounceRendering) || o)($);\n}\nfunction $() {\n  for (var n, u, t, r, o, f, c, s = 1; i.length;) i.length > s && i.sort(e), n = i.shift(), s = i.length, n.__d && (t = void 0, o = (r = (u = n).__v).__e, f = [], c = [], u.__P && ((t = d({}, r)).__v = r.__v + 1, l.vnode && l.vnode(t), O(u.__P, t, r, u.__n, u.__P.namespaceURI, 32 & r.__u ? [o] : null, f, null == o ? S(r) : o, !!(32 & r.__u), c), t.__v = r.__v, t.__.__k[t.__i] = t, z(f, t, c), t.__e != o && C(t)));\n  $.__r = 0;\n}\nfunction I(n, l, u, t, i, r, o, e, f, c, s) {\n  var a,\n    h,\n    v,\n    w,\n    d,\n    g,\n    _ = t && t.__k || y,\n    m = l.length;\n  for (f = P(u, l, _, f, m), a = 0; a < m; a++) null != (v = u.__k[a]) && (h = -1 == v.__i ? p : _[v.__i] || p, v.__i = a, g = O(n, v, h, i, r, o, e, f, c, s), w = v.__e, v.ref && h.ref != v.ref && (h.ref && q(h.ref, null, v), s.push(v.ref, v.__c || w, v)), null == d && null != w && (d = w), 4 & v.__u || h.__k === v.__k ? f = A(v, f, n) : \"function\" == typeof v.type && void 0 !== g ? f = g : w && (f = w.nextSibling), v.__u &= -7);\n  return u.__e = d, f;\n}\nfunction P(n, l, u, t, i) {\n  var r,\n    o,\n    e,\n    f,\n    c,\n    s = u.length,\n    a = s,\n    h = 0;\n  for (n.__k = new Array(i), r = 0; r < i; r++) null != (o = l[r]) && \"boolean\" != typeof o && \"function\" != typeof o ? (f = r + h, (o = n.__k[r] = \"string\" == typeof o || \"number\" == typeof o || \"bigint\" == typeof o || o.constructor == String ? m(null, o, null, null, null) : w(o) ? m(k, {\n    children: o\n  }, null, null, null) : null == o.constructor && o.__b > 0 ? m(o.type, o.props, o.key, o.ref ? o.ref : null, o.__v) : o).__ = n, o.__b = n.__b + 1, e = null, -1 != (c = o.__i = L(o, u, f, a)) && (a--, (e = u[c]) && (e.__u |= 2)), null == e || null == e.__v ? (-1 == c && (i > s ? h-- : i < s && h++), \"function\" != typeof o.type && (o.__u |= 4)) : c != f && (c == f - 1 ? h-- : c == f + 1 ? h++ : (c > f ? h-- : h++, o.__u |= 4))) : n.__k[r] = null;\n  if (a) for (r = 0; r < s; r++) null != (e = u[r]) && 0 == (2 & e.__u) && (e.__e == t && (t = S(e)), B(e, e));\n  return t;\n}\nfunction A(n, l, u) {\n  var t, i;\n  if (\"function\" == typeof n.type) {\n    for (t = n.__k, i = 0; t && i < t.length; i++) t[i] && (t[i].__ = n, l = A(t[i], l, u));\n    return l;\n  }\n  n.__e != l && (l && n.type && !u.contains(l) && (l = S(n)), u.insertBefore(n.__e, l || null), l = n.__e);\n  do {\n    l = l && l.nextSibling;\n  } while (null != l && 8 == l.nodeType);\n  return l;\n}\nfunction H(n, l) {\n  return l = l || [], null == n || \"boolean\" == typeof n || (w(n) ? n.some(function (n) {\n    H(n, l);\n  }) : l.push(n)), l;\n}\nfunction L(n, l, u, t) {\n  var i,\n    r,\n    o = n.key,\n    e = n.type,\n    f = l[u];\n  if (null === f && null == n.key || f && o == f.key && e == f.type && 0 == (2 & f.__u)) return u;\n  if (t > (null != f && 0 == (2 & f.__u) ? 1 : 0)) for (i = u - 1, r = u + 1; i >= 0 || r < l.length;) {\n    if (i >= 0) {\n      if ((f = l[i]) && 0 == (2 & f.__u) && o == f.key && e == f.type) return i;\n      i--;\n    }\n    if (r < l.length) {\n      if ((f = l[r]) && 0 == (2 & f.__u) && o == f.key && e == f.type) return r;\n      r++;\n    }\n  }\n  return -1;\n}\nfunction T(n, l, u) {\n  \"-\" == l[0] ? n.setProperty(l, null == u ? \"\" : u) : n[l] = null == u ? \"\" : \"number\" != typeof u || v.test(l) ? u : u + \"px\";\n}\nfunction j(n, l, u, t, i) {\n  var r, o;\n  n: if (\"style\" == l) {\n    if (\"string\" == typeof u) n.style.cssText = u;else {\n      if (\"string\" == typeof t && (n.style.cssText = t = \"\"), t) for (l in t) u && l in u || T(n.style, l, \"\");\n      if (u) for (l in u) t && u[l] == t[l] || T(n.style, l, u[l]);\n    }\n  } else if (\"o\" == l[0] && \"n\" == l[1]) r = l != (l = l.replace(f, \"$1\")), o = l.toLowerCase(), l = o in n || \"onFocusOut\" == l || \"onFocusIn\" == l ? o.slice(2) : l.slice(2), n.l || (n.l = {}), n.l[l + r] = u, u ? t ? u.u = t.u : (u.u = c, n.addEventListener(l, r ? a : s, r)) : n.removeEventListener(l, r ? a : s, r);else {\n    if (\"http://www.w3.org/2000/svg\" == i) l = l.replace(/xlink(H|:h)/, \"h\").replace(/sName$/, \"s\");else if (\"width\" != l && \"height\" != l && \"href\" != l && \"list\" != l && \"form\" != l && \"tabIndex\" != l && \"download\" != l && \"rowSpan\" != l && \"colSpan\" != l && \"role\" != l && \"popover\" != l && l in n) try {\n      n[l] = null == u ? \"\" : u;\n      break n;\n    } catch (n) {}\n    \"function\" == typeof u || (null == u || !1 === u && \"-\" != l[4] ? n.removeAttribute(l) : n.setAttribute(l, \"popover\" == l && 1 == u ? \"\" : u));\n  }\n}\nfunction F(n) {\n  return function (u) {\n    if (this.l) {\n      var t = this.l[u.type + n];\n      if (null == u.t) u.t = c++;else if (u.t < t.u) return;\n      return t(l.event ? l.event(u) : u);\n    }\n  };\n}\nfunction O(n, u, t, i, r, o, e, f, c, s) {\n  var a,\n    h,\n    p,\n    y,\n    v,\n    _,\n    m,\n    b,\n    S,\n    C,\n    M,\n    $,\n    P,\n    A,\n    H,\n    L,\n    T,\n    j = u.type;\n  if (null != u.constructor) return null;\n  128 & t.__u && (c = !!(32 & t.__u), o = [f = u.__e = t.__e]), (a = l.__b) && a(u);\n  n: if (\"function\" == typeof j) try {\n    if (b = u.props, S = \"prototype\" in j && j.prototype.render, C = (a = j.contextType) && i[a.__c], M = a ? C ? C.props.value : a.__ : i, t.__c ? m = (h = u.__c = t.__c).__ = h.__E : (S ? u.__c = h = new j(b, M) : (u.__c = h = new x(b, M), h.constructor = j, h.render = D), C && C.sub(h), h.props = b, h.state || (h.state = {}), h.context = M, h.__n = i, p = h.__d = !0, h.__h = [], h._sb = []), S && null == h.__s && (h.__s = h.state), S && null != j.getDerivedStateFromProps && (h.__s == h.state && (h.__s = d({}, h.__s)), d(h.__s, j.getDerivedStateFromProps(b, h.__s))), y = h.props, v = h.state, h.__v = u, p) S && null == j.getDerivedStateFromProps && null != h.componentWillMount && h.componentWillMount(), S && null != h.componentDidMount && h.__h.push(h.componentDidMount);else {\n      if (S && null == j.getDerivedStateFromProps && b !== y && null != h.componentWillReceiveProps && h.componentWillReceiveProps(b, M), !h.__e && null != h.shouldComponentUpdate && !1 === h.shouldComponentUpdate(b, h.__s, M) || u.__v == t.__v) {\n        for (u.__v != t.__v && (h.props = b, h.state = h.__s, h.__d = !1), u.__e = t.__e, u.__k = t.__k, u.__k.some(function (n) {\n          n && (n.__ = u);\n        }), $ = 0; $ < h._sb.length; $++) h.__h.push(h._sb[$]);\n        h._sb = [], h.__h.length && e.push(h);\n        break n;\n      }\n      null != h.componentWillUpdate && h.componentWillUpdate(b, h.__s, M), S && null != h.componentDidUpdate && h.__h.push(function () {\n        h.componentDidUpdate(y, v, _);\n      });\n    }\n    if (h.context = M, h.props = b, h.__P = n, h.__e = !1, P = l.__r, A = 0, S) {\n      for (h.state = h.__s, h.__d = !1, P && P(u), a = h.render(h.props, h.state, h.context), H = 0; H < h._sb.length; H++) h.__h.push(h._sb[H]);\n      h._sb = [];\n    } else do {\n      h.__d = !1, P && P(u), a = h.render(h.props, h.state, h.context), h.state = h.__s;\n    } while (h.__d && ++A < 25);\n    h.state = h.__s, null != h.getChildContext && (i = d(d({}, i), h.getChildContext())), S && !p && null != h.getSnapshotBeforeUpdate && (_ = h.getSnapshotBeforeUpdate(y, v)), L = a, null != a && a.type === k && null == a.key && (L = N(a.props.children)), f = I(n, w(L) ? L : [L], u, t, i, r, o, e, f, c, s), h.base = u.__e, u.__u &= -161, h.__h.length && e.push(h), m && (h.__E = h.__ = null);\n  } catch (n) {\n    if (u.__v = null, c || null != o) {\n      if (n.then) {\n        for (u.__u |= c ? 160 : 128; f && 8 == f.nodeType && f.nextSibling;) f = f.nextSibling;\n        o[o.indexOf(f)] = null, u.__e = f;\n      } else for (T = o.length; T--;) g(o[T]);\n    } else u.__e = t.__e, u.__k = t.__k;\n    l.__e(n, u, t);\n  } else null == o && u.__v == t.__v ? (u.__k = t.__k, u.__e = t.__e) : f = u.__e = V(t.__e, u, t, i, r, o, e, c, s);\n  return (a = l.diffed) && a(u), 128 & u.__u ? void 0 : f;\n}\nfunction z(n, u, t) {\n  for (var i = 0; i < t.length; i++) q(t[i], t[++i], t[++i]);\n  l.__c && l.__c(u, n), n.some(function (u) {\n    try {\n      n = u.__h, u.__h = [], n.some(function (n) {\n        n.call(u);\n      });\n    } catch (n) {\n      l.__e(n, u.__v);\n    }\n  });\n}\nfunction N(n) {\n  return \"object\" != typeof n || null == n || n.__b && n.__b > 0 ? n : w(n) ? n.map(N) : d({}, n);\n}\nfunction V(u, t, i, r, o, e, f, c, s) {\n  var a,\n    h,\n    y,\n    v,\n    d,\n    _,\n    m,\n    b = i.props,\n    k = t.props,\n    x = t.type;\n  if (\"svg\" == x ? o = \"http://www.w3.org/2000/svg\" : \"math\" == x ? o = \"http://www.w3.org/1998/Math/MathML\" : o || (o = \"http://www.w3.org/1999/xhtml\"), null != e) for (a = 0; a < e.length; a++) if ((d = e[a]) && \"setAttribute\" in d == !!x && (x ? d.localName == x : 3 == d.nodeType)) {\n    u = d, e[a] = null;\n    break;\n  }\n  if (null == u) {\n    if (null == x) return document.createTextNode(k);\n    u = document.createElementNS(o, x, k.is && k), c && (l.__m && l.__m(t, e), c = !1), e = null;\n  }\n  if (null == x) b === k || c && u.data == k || (u.data = k);else {\n    if (e = e && n.call(u.childNodes), b = i.props || p, !c && null != e) for (b = {}, a = 0; a < u.attributes.length; a++) b[(d = u.attributes[a]).name] = d.value;\n    for (a in b) if (d = b[a], \"children\" == a) ;else if (\"dangerouslySetInnerHTML\" == a) y = d;else if (!(a in k)) {\n      if (\"value\" == a && \"defaultValue\" in k || \"checked\" == a && \"defaultChecked\" in k) continue;\n      j(u, a, null, d, o);\n    }\n    for (a in k) d = k[a], \"children\" == a ? v = d : \"dangerouslySetInnerHTML\" == a ? h = d : \"value\" == a ? _ = d : \"checked\" == a ? m = d : c && \"function\" != typeof d || b[a] === d || j(u, a, d, b[a], o);\n    if (h) c || y && (h.__html == y.__html || h.__html == u.innerHTML) || (u.innerHTML = h.__html), t.__k = [];else if (y && (u.innerHTML = \"\"), I(\"template\" == t.type ? u.content : u, w(v) ? v : [v], t, i, r, \"foreignObject\" == x ? \"http://www.w3.org/1999/xhtml\" : o, e, f, e ? e[0] : i.__k && S(i, 0), c, s), null != e) for (a = e.length; a--;) g(e[a]);\n    c || (a = \"value\", \"progress\" == x && null == _ ? u.removeAttribute(\"value\") : null != _ && (_ !== u[a] || \"progress\" == x && !_ || \"option\" == x && _ != b[a]) && j(u, a, _, b[a], o), a = \"checked\", null != m && m != u[a] && j(u, a, m, b[a], o));\n  }\n  return u;\n}\nfunction q(n, u, t) {\n  try {\n    if (\"function\" == typeof n) {\n      var i = \"function\" == typeof n.__u;\n      i && n.__u(), i && null == u || (n.__u = n(u));\n    } else n.current = u;\n  } catch (n) {\n    l.__e(n, t);\n  }\n}\nfunction B(n, u, t) {\n  var i, r;\n  if (l.unmount && l.unmount(n), (i = n.ref) && (i.current && i.current != n.__e || q(i, null, u)), null != (i = n.__c)) {\n    if (i.componentWillUnmount) try {\n      i.componentWillUnmount();\n    } catch (n) {\n      l.__e(n, u);\n    }\n    i.base = i.__P = null;\n  }\n  if (i = n.__k) for (r = 0; r < i.length; r++) i[r] && B(i[r], u, t || \"function\" != typeof n.type);\n  t || g(n.__e), n.__c = n.__ = n.__e = void 0;\n}\nfunction D(n, l, u) {\n  return this.constructor(n, u);\n}\nfunction E(u, t, i) {\n  var r, o, e, f;\n  t == document && (t = document.documentElement), l.__ && l.__(u, t), o = (r = \"function\" == typeof i) ? null : i && i.__k || t.__k, e = [], f = [], O(t, u = (!r && i || t).__k = _(k, null, [u]), o || p, p, t.namespaceURI, !r && i ? [i] : o ? null : t.firstChild ? n.call(t.childNodes) : null, e, !r && i ? i : o ? o.__e : t.firstChild, r, f), z(e, u, f);\n}\nfunction G(n, l) {\n  E(n, l, G);\n}\nfunction J(l, u, t) {\n  var i,\n    r,\n    o,\n    e,\n    f = d({}, l.props);\n  for (o in l.type && l.type.defaultProps && (e = l.type.defaultProps), u) \"key\" == o ? i = u[o] : \"ref\" == o ? r = u[o] : f[o] = null == u[o] && null != e ? e[o] : u[o];\n  return arguments.length > 2 && (f.children = arguments.length > 3 ? n.call(arguments, 2) : t), m(l.type, f, i || l.key, r || l.ref, null);\n}\nfunction K(n) {\n  function l(n) {\n    var u, t;\n    return this.getChildContext || (u = new Set(), (t = {})[l.__c] = this, this.getChildContext = function () {\n      return t;\n    }, this.componentWillUnmount = function () {\n      u = null;\n    }, this.shouldComponentUpdate = function (n) {\n      this.props.value != n.value && u.forEach(function (n) {\n        n.__e = !0, M(n);\n      });\n    }, this.sub = function (n) {\n      u.add(n);\n      var l = n.componentWillUnmount;\n      n.componentWillUnmount = function () {\n        u && u.delete(n), l && l.call(n);\n      };\n    }), n.children;\n  }\n  return l.__c = \"__cC\" + h++, l.__ = n, l.Provider = l.__l = (l.Consumer = function (n, l) {\n    return n.children(l);\n  }).contextType = l, l;\n}\nn = y.slice, l = {\n  __e: function (n, l, u, t) {\n    for (var i, r, o; l = l.__;) if ((i = l.__c) && !i.__) try {\n      if ((r = i.constructor) && null != r.getDerivedStateFromError && (i.setState(r.getDerivedStateFromError(n)), o = i.__d), null != i.componentDidCatch && (i.componentDidCatch(n, t || {}), o = i.__d), o) return i.__E = i;\n    } catch (l) {\n      n = l;\n    }\n    throw n;\n  }\n}, u = 0, t = function (n) {\n  return null != n && null == n.constructor;\n}, x.prototype.setState = function (n, l) {\n  var u;\n  u = null != this.__s && this.__s != this.state ? this.__s : this.__s = d({}, this.state), \"function\" == typeof n && (n = n(d({}, u), this.props)), n && d(u, n), null != n && this.__v && (l && this._sb.push(l), M(this));\n}, x.prototype.forceUpdate = function (n) {\n  this.__v && (this.__e = !0, n && this.__h.push(n), M(this));\n}, x.prototype.render = k, i = [], o = \"function\" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, e = function (n, l) {\n  return n.__v.__b - l.__v.__b;\n}, $.__r = 0, f = /(PointerCapture)$|Capture$/i, c = 0, s = F(!1), a = F(!0), h = 0;\nexport { x as Component, k as Fragment, J as cloneElement, K as createContext, _ as createElement, b as createRef, _ as h, G as hydrate, t as isValidElement, l as options, E as render, H as toChildArray };", "map": {"version": 3, "names": ["n", "l", "u", "t", "i", "r", "o", "e", "f", "c", "s", "a", "h", "p", "y", "v", "w", "Array", "isArray", "d", "g", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_", "arguments", "length", "children", "call", "defaultProps", "m", "type", "props", "key", "ref", "__k", "__", "__b", "__e", "__c", "constructor", "__v", "__i", "__u", "vnode", "b", "current", "k", "x", "context", "S", "C", "base", "M", "__d", "push", "$", "__r", "debounceRendering", "sort", "shift", "__P", "O", "__n", "namespaceURI", "z", "I", "P", "q", "A", "nextS<PERSON>ling", "String", "L", "B", "contains", "insertBefore", "nodeType", "H", "some", "T", "setProperty", "test", "j", "style", "cssText", "replace", "toLowerCase", "slice", "addEventListener", "removeEventListener", "removeAttribute", "setAttribute", "F", "event", "prototype", "render", "contextType", "value", "__E", "D", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "N", "then", "indexOf", "V", "diffed", "map", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "name", "__html", "innerHTML", "content", "unmount", "componentWillUnmount", "E", "documentElement", "<PERSON><PERSON><PERSON><PERSON>", "G", "J", "K", "Set", "for<PERSON>ach", "add", "delete", "Provider", "__l", "Consumer", "getDerivedStateFromError", "setState", "componentDidCatch", "isValidElement", "forceUpdate", "Promise", "bind", "resolve", "setTimeout", "depthSort", "Component", "Fragment", "cloneElement", "createContext", "createElement", "createRef", "hydrate", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/constants.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/util.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/options.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/create-element.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/component.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/diff/props.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/create-context.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/diff/children.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/diff/index.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/render.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/clone-element.js", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/preact/src/diff/catch-error.js"], "sourcesContent": ["/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 2;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 1;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\nexport const XHTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\nexport const MATH_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n\nexport const NULL = null;\nexport const UNDEFINED = undefined;\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {import('./index').ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\nimport { NULL, UNDEFINED } from './constants';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != NULL) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] == UNDEFINED) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, NULL);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {import('./internal').VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: NULL,\n\t\t_parent: NULL,\n\t\t_depth: 0,\n\t\t_dom: NULL,\n\t\t_component: NULL,\n\t\tconstructor: UNDEFINED,\n\t\t_original: original == NULL ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == NULL && options.vnode != NULL) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: NULL };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != NULL && vnode.constructor == UNDEFINED;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE, NULL } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {import('./internal').Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != NULL && this._nextState != this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == NULL) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {import('./internal').Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](https://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == NULL) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: NULL;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != NULL && sibling._dom != NULL) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : NULL;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : NULL,\n\t\t\tcommitQueue,\n\t\t\toldDom == NULL ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != NULL && vnode._component != NULL) {\n\t\tvnode._dom = vnode._component.base = NULL;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != NULL && child._dom != NULL) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce != options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {import('./internal').Component} a\n * @param {import('./internal').Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c,\n\t\tl = 1;\n\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile (rerenderQueue.length) {\n\t\t// Keep the rerender queue sorted by (depth, insertion order). The queue\n\t\t// will initially be sorted on the first iteration only if it has more than 1 item.\n\t\t//\n\t\t// New items can be added to the queue e.g. when rerendering a provider, so we want to\n\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t// single pass\n\t\tif (rerenderQueue.length > l) {\n\t\t\trerenderQueue.sort(depthSort);\n\t\t}\n\n\t\tc = rerenderQueue.shift();\n\t\tl = rerenderQueue.length;\n\n\t\tif (c._dirty) {\n\t\t\trenderComponent(c);\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL, NULL, SVG_NAMESPACE } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] == '-') {\n\t\tstyle.setProperty(key, value == NULL ? '' : value);\n\t} else if (value == NULL) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\nconst CAPTURE_REGEX = /(PointerCapture)$|Capture$/i;\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name == 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] != oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] == 'o' && name[1] == 'n') {\n\t\tuseCapture = name != (name = name.replace(CAPTURE_REGEX, '$1'));\n\t\tconst lowerCaseName = name.toLowerCase();\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (lowerCaseName in dom || name == 'onFocusOut' || name == 'onFocusIn')\n\t\t\tname = lowerCaseName.slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == SVG_NAMESPACE) {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == NULL ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != NULL && (value !== false || name[4] == '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {import('../internal').PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == NULL) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\nimport { NULL } from './constants';\n\nexport let i = 0;\n\nexport function createContext(defaultValue) {\n\tfunction Context(props) {\n\t\tif (!this.getChildContext) {\n\t\t\t/** @type {Set<import('./internal').Component> | null} */\n\t\t\tlet subs = new Set();\n\t\t\tlet ctx = {};\n\t\t\tctx[Context._id] = this;\n\n\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\tsubs = NULL;\n\t\t\t};\n\n\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t// @ts-expect-error even\n\t\t\t\tif (this.props.value != _props.value) {\n\t\t\t\t\tsubs.forEach(c => {\n\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.sub = c => {\n\t\t\t\tsubs.add(c);\n\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\tif (subs) {\n\t\t\t\t\t\tsubs.delete(c);\n\t\t\t\t\t}\n\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t};\n\t\t\t};\n\t\t}\n\n\t\treturn props.children;\n\t}\n\n\tContext._id = '__cC' + i++;\n\tContext._defaultValue = defaultValue;\n\n\t/** @type {import('./internal').FunctionComponent} */\n\tContext.Consumer = (props, contextValue) => {\n\t\treturn props.children(contextValue);\n\t};\n\n\t// we could also get rid of _contextRef entirely\n\tContext.Provider =\n\t\tContext._contextRef =\n\t\tContext.Consumer.contextType =\n\t\t\tContext;\n\n\treturn Context;\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport {\n\tEMPTY_OBJ,\n\tEMPTY_ARR,\n\tINSERT_VNODE,\n\tMATCHED,\n\tUNDEFINED,\n\tNULL\n} from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\toldDom = constructNewChildrenArray(\n\t\tnewParentVNode,\n\t\trenderResult,\n\t\toldChildren,\n\t\toldDom,\n\t\tnewChildrenLength\n\t);\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == NULL) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index == -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tlet result = diff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, NULL, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == NULL && newDom != NULL) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (typeof childVNode.type == 'function' && result !== UNDEFINED) {\n\t\t\toldDom = result;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(\n\tnewParentVNode,\n\trenderResult,\n\toldChildren,\n\toldDom,\n\tnewChildrenLength\n) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = new Array(newChildrenLength);\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == NULL ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tnewParentVNode._children[i] = NULL;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tNULL,\n\t\t\t\tchildVNode,\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (childVNode.constructor == UNDEFINED && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : NULL,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = NULL;\n\t\tif (matchingIndex != -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original == null\n\t\tconst isMounting = oldVNode == NULL || oldVNode._original == NULL;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\t// When the array of children is growing we need to decrease the skew\n\t\t\t\t// as we are adding a new element to the array.\n\t\t\t\t// Example:\n\t\t\t\t// [1, 2, 3] --> [0, 1, 2, 3]\n\t\t\t\t// oldChildren   newChildren\n\t\t\t\t//\n\t\t\t\t// The new element is at index 0, so our skew is 0,\n\t\t\t\t// we need to decrease the skew as we are adding a new element.\n\t\t\t\t// The decrease will cause us to compare the element at position 1\n\t\t\t\t// with value 1 with the element at position 0 with value 0.\n\t\t\t\t//\n\t\t\t\t// A linear concept is applied when the array is shrinking,\n\t\t\t\t// if the length is unchanged we can assume that no skew\n\t\t\t\t// changes are needed.\n\t\t\t\tif (newChildrenLength > oldChildrenLength) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else if (newChildrenLength < oldChildrenLength) {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex != skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != NULL && (oldVNode._flags & MATCHED) == 0) {\n\t\t\t\tif (oldVNode._dom == oldDom) {\n\t\t\t\t\toldDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || NULL);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != NULL && oldDom.nodeType == 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == NULL || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\t//\n\t// If there is an unkeyed functional VNode, that isn't a built-in like our Fragment,\n\t// we should not search as we risk re-using state of an unrelated VNode. (reverted for now)\n\tlet shouldSearch =\n\t\t// (typeof type != 'function' || type === Fragment || key) &&\n\t\tremainingOldChildren >\n\t\t(oldVNode != NULL && (oldVNode._flags & MATCHED) == 0 ? 1 : 0);\n\n\tif (\n\t\t(oldVNode === NULL && childVNode.key == null) ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype == oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) == 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\tlet x = skewedIndex - 1;\n\t\tlet y = skewedIndex + 1;\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMATH_NAMESPACE,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tNULL,\n\tRESET_MODE,\n\tSVG_NAMESPACE,\n\tUNDEFINED,\n\tXHTML_NAMESPACE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * @template {any} T\n * @typedef {import('../internal').Ref<T>} Ref<T>\n */\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor != UNDEFINED) return NULL;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == NULL) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != NULL) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tc.componentWillMount != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != NULL &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original == oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original != oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != NULL) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != NULL) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != NULL) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != NULL && tmp.type === Fragment && tmp.key == NULL;\n\t\t\tlet renderResult = tmp;\n\n\t\t\tif (isTopLevelFragment) {\n\t\t\t\trenderResult = cloneNode(tmp.props.children);\n\t\t\t}\n\n\t\t\toldDom = diffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = NULL;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = NULL;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != NULL) {\n\t\t\t\tif (e.then) {\n\t\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t\t: MODE_SUSPENDED;\n\n\t\t\t\t\twhile (oldDom && oldDom.nodeType == 8 && oldDom.nextSibling) {\n\t\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t\t}\n\n\t\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = NULL;\n\t\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t\t} else {\n\t\t\t\t\tfor (let i = excessDomChildren.length; i--; ) {\n\t\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == NULL &&\n\t\tnewVNode._original == oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\toldDom = newVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n\n\treturn newVNode._flags & MODE_SUSPENDED ? undefined : oldDom;\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\nfunction cloneNode(node) {\n\tif (\n\t\ttypeof node != 'object' ||\n\t\tnode == NULL ||\n\t\t(node._depth && node._depth > 0)\n\t) {\n\t\treturn node;\n\t}\n\n\tif (isArray(node)) {\n\t\treturn node.map(cloneNode);\n\t}\n\n\treturn assign({}, node);\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType == 'svg') namespace = SVG_NAMESPACE;\n\telse if (nodeType == 'math') namespace = MATH_NAMESPACE;\n\telse if (!namespace) namespace = XHTML_NAMESPACE;\n\n\tif (excessDomChildren != NULL) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value == !!nodeType &&\n\t\t\t\t(nodeType ? value.localName == nodeType : value.nodeType == 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = NULL;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == NULL) {\n\t\tif (nodeType == NULL) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = NULL;\n\t}\n\n\tif (nodeType == NULL) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data != newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != NULL) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, NULL, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html != oldHtml.__html && newHtml.__html != dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\t// @ts-expect-error\n\t\t\t\tnewVNode.type == 'template' ? dom.content : dom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType == 'foreignObject' ? XHTML_NAMESPACE : namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != NULL) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType == 'progress' && inputValue == NULL) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue != UNDEFINED &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType == 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType == 'option' && inputValue != oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked != UNDEFINED && checked != dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != NULL) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current == vnode._dom) {\n\t\t\tapplyRef(r, NULL, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != NULL) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = NULL;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\tvnode._component = vnode._parent = vnode._dom = UNDEFINED;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ, NULL } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to render into\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\t// https://github.com/preactjs/preact/issues/3794\n\tif (parentDom == document) {\n\t\tparentDom = document.documentElement;\n\t}\n\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? NULL\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, NULL, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? NULL\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: NULL,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\nimport { NULL, UNDEFINED } from './constants';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] == UNDEFINED && defaultProps != UNDEFINED) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tNULL\n\t);\n}\n", "import { NULL } from '../constants';\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {import('../internal').VNode} [oldVNode]\n * @param {import('../internal').ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {import('../internal').Component} */\n\tlet component,\n\t\t/** @type {import('../internal').ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != NULL) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != NULL) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n"], "mappings": "AACO,IC0BMA,CAAA;EChBPC,CAAA;ECPFC,CAAA;EA2FSC,CAAA;ECmFTC,CAAA;EAWAC,CAAA;EAEEC,CAAA;EA0BAC,CAAA;EC1MAC,CAAA;EAaFC,CAAA;EA+IEC,CAAA;EACAC,CAAA;ECzKKC,CAAA;ENeEC,CAAA,GAAgC,CAAG;EACnCC,CAAA,GAAY;EACZC,CAAA,GACZ;ECnBYC,CAAA,GAAUC,KAAA,CAAMC,OAAA;AAStB,SAASC,EAAOnB,CAAA,EAAKC,CAAA;EAE3B,KAAK,IAAIC,CAAA,IAAKD,CAAA,EAAOD,CAAA,CAAIE,CAAA,IAAKD,CAAA,CAAMC,CAAA;EACpC,OAA6BF,CAC9B;AAAA;AAQgB,SAAAoB,EAAWpB,CAAA;EACtBA,CAAA,IAAQA,CAAA,CAAKqB,UAAA,IAAYrB,CAAA,CAAKqB,UAAA,CAAWC,WAAA,CAAYtB,CAAA,CAC1D;AAAA;AEVgB,SAAAuB,EAActB,CAAA,EAAMC,CAAA,EAAOC,CAAA;EAC1C,IACCC,CAAA;IACAC,CAAA;IACAC,CAAA;IAHGC,CAAA,GAAkB;EAItB,KAAKD,CAAA,IAAKJ,CAAA,EACA,SAALI,CAAA,GAAYF,CAAA,GAAMF,CAAA,CAAMI,CAAA,IACd,SAALA,CAAA,GAAYD,CAAA,GAAMH,CAAA,CAAMI,CAAA,IAC5BC,CAAA,CAAgBD,CAAA,IAAKJ,CAAA,CAAMI,CAAA;EAUjC,IAPIkB,SAAA,CAAUC,MAAA,GAAS,MACtBlB,CAAA,CAAgBmB,QAAA,GACfF,SAAA,CAAUC,MAAA,GAAS,IAAIzB,CAAA,CAAM2B,IAAA,CAAKH,SAAA,EAAW,KAAKrB,CAAA,GAKjC,qBAARF,CAAA,IHjBQ,QGiBcA,CAAA,CAAK2B,YAAA,EACrC,KAAKtB,CAAA,IAAKL,CAAA,CAAK2B,YAAA,EHjBQ,QGkBlBrB,CAAA,CAAgBD,CAAA,MACnBC,CAAA,CAAgBD,CAAA,IAAKL,CAAA,CAAK2B,YAAA,CAAatB,CAAA;EAK1C,OAAOuB,CAAA,CAAY5B,CAAA,EAAMM,CAAA,EAAiBH,CAAA,EAAKC,CAAA,EHzB5B,KG0BpB;AAAA;AAcgB,SAAAwB,EAAY7B,CAAA,EAAMG,CAAA,EAAOC,CAAA,EAAKC,CAAA,EAAKC,CAAA;EAIlD,IAAMC,CAAA,GAAQ;IACbuB,IAAA,EAAA9B,CAAA;IACA+B,KAAA,EAAA5B,CAAA;IACA6B,GAAA,EAAA5B,CAAA;IACA6B,GAAA,EAAA5B,CAAA;IACA6B,GAAA,EHjDkB;IGkDlBC,EAAA,EHlDkB;IGmDlBC,GAAA,EAAQ;IACRC,GAAA,EHpDkB;IGqDlBC,GAAA,EHrDkB;IGsDlBC,WAAA,OHrDuB;IGsDvBC,GAAA,EHvDkB,QGuDPlC,CAAA,KAAqBJ,CAAA,GAAUI,CAAA;IAC1CmC,GAAA,GAAS;IACTC,GAAA,EAAQ;EAAA;EAMT,OH/DmB,QG6DfpC,CAAA,IH7De,QG6DKL,CAAA,CAAQ0C,KAAA,IAAe1C,CAAA,CAAQ0C,KAAA,CAAMpC,CAAA,GAEtDA,CACR;AAAA;AAAA,SAEgBqC,EAAA;EACf,OAAO;IAAEC,OAAA,EHnEU;EAAA,CGoEpB;AAAA;AAEgB,SAAAC,EAAS9C,CAAA;EACxB,OAAOA,CAAA,CAAM0B,QACd;AAAA;AC3EO,SAASqB,EAAc/C,CAAA,EAAOC,CAAA;EACpC,KAAK8B,KAAA,GAAQ/B,CAAA,EACb,KAAKgD,OAAA,GAAU/C,CAChB;AAAA;AAAA,SA0EgBgD,EAAcjD,CAAA,EAAOC,CAAA;EACpC,IJ3EmB,QI2EfA,CAAA,EAEH,OAAOD,CAAA,CAAKmC,EAAA,GACTc,CAAA,CAAcjD,CAAA,CAAKmC,EAAA,EAAUnC,CAAA,CAAKyC,GAAA,GAAU,KJ9E7B;EImFnB,KADA,IAAIvC,CAAA,EACGD,CAAA,GAAaD,CAAA,CAAKkC,GAAA,CAAWT,MAAA,EAAQxB,CAAA,IAG3C,IJtFkB,SIoFlBC,CAAA,GAAUF,CAAA,CAAKkC,GAAA,CAAWjC,CAAA,MJpFR,QIsFKC,CAAA,CAAOmC,GAAA,EAI7B,OAAOnC,CAAA,CAAOmC,GAAA;EAShB,OAA4B,qBAAdrC,CAAA,CAAM8B,IAAA,GAAqBmB,CAAA,CAAcjD,CAAA,IJnGpC,IIoGpB;AAAA;AA2CA,SAASkD,EAAwBlD,CAAA;EAAjC,IAGWC,CAAA,EACJC,CAAA;EAHN,IJhJmB,SIgJdF,CAAA,GAAQA,CAAA,CAAKmC,EAAA,KJhJC,QIgJoBnC,CAAA,CAAKsC,GAAA,EAAqB;IAEhE,KADAtC,CAAA,CAAKqC,GAAA,GAAQrC,CAAA,CAAKsC,GAAA,CAAYa,IAAA,GJjJZ,MIkJTlD,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,CAAKkC,GAAA,CAAWT,MAAA,EAAQxB,CAAA,IAE3C,IJpJiB,SImJbC,CAAA,GAAQF,CAAA,CAAKkC,GAAA,CAAWjC,CAAA,MJnJX,QIoJIC,CAAA,CAAKmC,GAAA,EAAe;MACxCrC,CAAA,CAAKqC,GAAA,GAAQrC,CAAA,CAAKsC,GAAA,CAAYa,IAAA,GAAOjD,CAAA,CAAKmC,GAAA;MAC1C;IACD;IAGD,OAAOa,CAAA,CAAwBlD,CAAA,CAChC;EAAA;AACD;AA4BgB,SAAAoD,EAAcpD,CAAA;EAAA,EAE1BA,CAAA,CAACqD,GAAA,KACDrD,CAAA,CAACqD,GAAA,IAAU,MACZjD,CAAA,CAAckD,IAAA,CAAKtD,CAAA,MAClBuD,CAAA,CAAOC,GAAA,MACTnD,CAAA,IAAgBJ,CAAA,CAAQwD,iBAAA,OAExBpD,CAAA,GAAeJ,CAAA,CAAQwD,iBAAA,KACNnD,CAAA,EAAOiD,CAAA,CAE1B;AAAA;AASA,SAASA,EAAA;EAMR,KALA,IAAIvD,CAAA,EAnGoBE,CAAA,EAOjBC,CAAA,EANHE,CAAA,EACHC,CAAA,EACAE,CAAA,EACAC,CAAA,EAgGAC,CAAA,GAAI,GAIEN,CAAA,CAAcqB,MAAA,GAOhBrB,CAAA,CAAcqB,MAAA,GAASf,CAAA,IAC1BN,CAAA,CAAcsD,IAAA,CAAKnD,CAAA,GAGpBP,CAAA,GAAII,CAAA,CAAcuD,KAAA,IAClBjD,CAAA,GAAIN,CAAA,CAAcqB,MAAA,EAEdzB,CAAA,CAACqD,GAAA,KA/GClD,CAAA,WALNG,CAAA,IADGD,CAAA,IADoBH,CAAA,GAuHNF,CAAA,EAtHMwC,GAAA,EACNH,GAAA,EACjB7B,CAAA,GAAc,IACdC,CAAA,GAAW,IAERP,CAAA,CAAS0D,GAAA,MACNzD,CAAA,GAAWgB,CAAA,CAAO,IAAId,CAAA,GACpBmC,GAAA,GAAanC,CAAA,CAAQmC,GAAA,GAAa,GACtCvC,CAAA,CAAQ0C,KAAA,IAAO1C,CAAA,CAAQ0C,KAAA,CAAMxC,CAAA,GAEjC0D,CAAA,CACC3D,CAAA,CAAS0D,GAAA,EACTzD,CAAA,EACAE,CAAA,EACAH,CAAA,CAAS4D,GAAA,EACT5D,CAAA,CAAS0D,GAAA,CAAYG,YAAA,EJzII,KI0IzB1D,CAAA,CAAQqC,GAAA,GAAyB,CAACpC,CAAA,IJ3HjB,MI4HjBE,CAAA,EJ5HiB,QI6HjBF,CAAA,GAAiB2C,CAAA,CAAc5C,CAAA,IAAYC,CAAA,KJ5IlB,KI6ItBD,CAAA,CAAQqC,GAAA,GACXjC,CAAA,GAGDN,CAAA,CAAQqC,GAAA,GAAanC,CAAA,CAAQmC,GAAA,EAC7BrC,CAAA,CAAQgC,EAAA,CAAAD,GAAA,CAAmB/B,CAAA,CAAQsC,GAAA,IAAWtC,CAAA,EAC9C6D,CAAA,CAAWxD,CAAA,EAAaL,CAAA,EAAUM,CAAA,GAE9BN,CAAA,CAAQkC,GAAA,IAAS/B,CAAA,IACpB4C,CAAA,CAAwB/C,CAAA;EA6F1BoD,CAAA,CAAOC,GAAA,GAAkB,CAC1B;AAAA;AAAA,SG3MgBS,EACfjE,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EAAA,IAEIC,CAAA;IAEHC,CAAA;IAEAG,CAAA;IAEAC,CAAA;IAEAG,CAAA;IAiCIC,CAAA;IA5BDG,CAAA,GAAepB,CAAA,IAAkBA,CAAA,CAAc+B,GAAA,IAAepB,CAAA;IAE9De,CAAA,GAAoB5B,CAAA,CAAawB,MAAA;EAUrC,KARAjB,CAAA,GAAS0D,CAAA,CACRhE,CAAA,EACAD,CAAA,EACAsB,CAAA,EACAf,CAAA,EACAqB,CAAA,GAGIlB,CAAA,GAAI,GAAGA,CAAA,GAAIkB,CAAA,EAAmBlB,CAAA,IPhEhB,SOiElBI,CAAA,GAAab,CAAA,CAAcgC,GAAA,CAAWvB,CAAA,OAMrCC,CAAA,IADyB,KAAtBG,CAAA,CAAU0B,GAAA,GACF5B,CAAA,GAEAU,CAAA,CAAYR,CAAA,CAAU0B,GAAA,KAAY5B,CAAA,EAI9CE,CAAA,CAAU0B,GAAA,GAAU9B,CAAA,EAGhBS,CAAA,GAASyC,CAAA,CACZ7D,CAAA,EACAe,CAAA,EACAH,CAAA,EACAR,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,GAIDM,CAAA,GAASD,CAAA,CAAUsB,GAAA,EACftB,CAAA,CAAWkB,GAAA,IAAOrB,CAAA,CAASqB,GAAA,IAAOlB,CAAA,CAAWkB,GAAA,KAC5CrB,CAAA,CAASqB,GAAA,IACZkC,CAAA,CAASvD,CAAA,CAASqB,GAAA,EPjGF,MOiGalB,CAAA,GAE9BL,CAAA,CAAS4C,IAAA,CACRvC,CAAA,CAAWkB,GAAA,EACXlB,CAAA,CAAUuB,GAAA,IAAetB,CAAA,EACzBD,CAAA,IPtGgB,QO0GdI,CAAA,IP1Gc,QO0GWH,CAAA,KAC5BG,CAAA,GAAgBH,CAAA,GPtHS,IO0HzBD,CAAA,CAAU2B,GAAA,IACV9B,CAAA,CAAQsB,GAAA,KAAenB,CAAA,CAAUmB,GAAA,GAEjC1B,CAAA,GAAS4D,CAAA,CAAOrD,CAAA,EAAYP,CAAA,EAAQR,CAAA,IACA,qBAAnBe,CAAA,CAAWe,IAAA,SPlHN,MOkH4BV,CAAA,GAClDZ,CAAA,GAASY,CAAA,GACCJ,CAAA,KACVR,CAAA,GAASQ,CAAA,CAAOqD,WAAA,GAIjBtD,CAAA,CAAU2B,GAAA,KAAW;EAKtB,OAFAxC,CAAA,CAAcmC,GAAA,GAAQlB,CAAA,EAEfX,CACR;AAAA;AAOA,SAAS0D,EACRlE,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EALD,IAQKC,CAAA;IAEAC,CAAA;IAEAC,CAAA;IA8DGC,CAAA;IAOAC,CAAA;IAnEHC,CAAA,GAAoBR,CAAA,CAAYuB,MAAA;IACnCd,CAAA,GAAuBD,CAAA;IAEpBE,CAAA,GAAO;EAGX,KADAZ,CAAA,CAAckC,GAAA,GAAa,IAAIjB,KAAA,CAAMb,CAAA,GAChCC,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,EAAmBC,CAAA,IP3JhB,SO8JlBC,CAAA,GAAaL,CAAA,CAAaI,CAAA,MAIJ,oBAAdC,CAAA,IACc,qBAAdA,CAAA,IA8CFE,CAAA,GAAcH,CAAA,GAAIO,CAAA,GA/BvBN,CAAA,GAAaN,CAAA,CAAckC,GAAA,CAAW7B,CAAA,IANjB,mBAAdC,CAAA,IACc,mBAAdA,CAAA,IAEc,mBAAdA,CAAA,IACPA,CAAA,CAAWiC,WAAA,IAAe+B,MAAA,GAEiBzC,CAAA,CPlL1B,MOoLhBvB,CAAA,EPpLgB,oBOyLPU,CAAA,CAAQV,CAAA,IACyBuB,CAAA,CAC1CiB,CAAA,EACA;IAAEpB,QAAA,EAAUpB;EAAA,GP5LI,oBACK,QOgMZA,CAAA,CAAWiC,WAAA,IAA4BjC,CAAA,CAAU8B,GAAA,GAAU,IAK1BP,CAAA,CAC1CvB,CAAA,CAAWwB,IAAA,EACXxB,CAAA,CAAWyB,KAAA,EACXzB,CAAA,CAAW0B,GAAA,EACX1B,CAAA,CAAW2B,GAAA,GAAM3B,CAAA,CAAW2B,GAAA,GP1MZ,MO2MhB3B,CAAA,CAAUkC,GAAA,IAGgClC,CAAA,EAIlC6B,EAAA,GAAWnC,CAAA,EACrBM,CAAA,CAAU8B,GAAA,GAAUpC,CAAA,CAAcoC,GAAA,GAAU,GAY5C7B,CAAA,GP/NkB,OOgOI,MARhBE,CAAA,GAAiBH,CAAA,CAAUmC,GAAA,GAAU8B,CAAA,CAC1CjE,CAAA,EACAJ,CAAA,EACAM,CAAA,EACAG,CAAA,OAMAA,CAAA,KADAJ,CAAA,GAAWL,CAAA,CAAYO,CAAA,OAGtBF,CAAA,CAAQmC,GAAA,IP7OW,KASH,QO2OCnC,CAAA,IP3OD,QO2OqBA,CAAA,CAAQiC,GAAA,KAGxB,KAAlB/B,CAAA,KAeCL,CAAA,GAAoBM,CAAA,GACvBE,CAAA,KACUR,CAAA,GAAoBM,CAAA,IAC9BE,CAAA,KAK4B,qBAAnBN,CAAA,CAAWwB,IAAA,KACrBxB,CAAA,CAAUoC,GAAA,IPjRc,MOmRfjC,CAAA,IAAiBD,CAAA,KAiBvBC,CAAA,IAAiBD,CAAA,GAAc,IAClCI,CAAA,KACUH,CAAA,IAAiBD,CAAA,GAAc,IACzCI,CAAA,MAEIH,CAAA,GAAgBD,CAAA,GACnBI,CAAA,KAEAA,CAAA,IAMDN,CAAA,CAAUoC,GAAA,IPlTc,OOgLzB1C,CAAA,CAAckC,GAAA,CAAW7B,CAAA,IPrKR;EOgTnB,IAAIM,CAAA,EACH,KAAKN,CAAA,GAAI,GAAGA,CAAA,GAAIK,CAAA,EAAmBL,CAAA,IPjTjB,SOkTjBE,CAAA,GAAWL,CAAA,CAAYG,CAAA,MACgC,MP5TnC,IO4TKE,CAAA,CAAQmC,GAAA,MAC5BnC,CAAA,CAAQ8B,GAAA,IAASlC,CAAA,KACpBA,CAAA,GAAS8C,CAAA,CAAc1C,CAAA,IAGxBiE,CAAA,CAAQjE,CAAA,EAAUA,CAAA;EAKrB,OAAOJ,CACR;AAAA;AAQA,SAASiE,EAAOpE,CAAA,EAAaC,CAAA,EAAQC,CAAA;EAArC,IAIMC,CAAA,EACKC,CAAA;EAFV,IAA+B,qBAApBJ,CAAA,CAAY8B,IAAA,EAAoB;IAE1C,KADI3B,CAAA,GAAWH,CAAA,CAAWkC,GAAA,EACjB9B,CAAA,GAAI,GAAGD,CAAA,IAAYC,CAAA,GAAID,CAAA,CAASsB,MAAA,EAAQrB,CAAA,IAC5CD,CAAA,CAASC,CAAA,MAKZD,CAAA,CAASC,CAAA,EAAE+B,EAAA,GAAWnC,CAAA,EACtBC,CAAA,GAASmE,CAAA,CAAOjE,CAAA,CAASC,CAAA,GAAIH,CAAA,EAAQC,CAAA;IAIvC,OAAOD,CACR;EAAA;EAAWD,CAAA,CAAWqC,GAAA,IAASpC,CAAA,KAC1BA,CAAA,IAAUD,CAAA,CAAY8B,IAAA,KAAS5B,CAAA,CAAUuE,QAAA,CAASxE,CAAA,MACrDA,CAAA,GAASgD,CAAA,CAAcjD,CAAA,IAExBE,CAAA,CAAUwE,YAAA,CAAa1E,CAAA,CAAWqC,GAAA,EAAOpC,CAAA,IP3VvB,OO4VlBA,CAAA,GAASD,CAAA,CAAWqC,GAAA;EAGrB;IACCpC,CAAA,GAASA,CAAA,IAAUA,CAAA,CAAOoE,WAAA;EAAA,SPhWR,QOiWVpE,CAAA,IAAqC,KAAnBA,CAAA,CAAO0E,QAAA;EAElC,OAAO1E,CACR;AAAA;AAAA,SAQgB2E,EAAa5E,CAAA,EAAUC,CAAA;EAUtC,OATAA,CAAA,GAAMA,CAAA,IAAO,IP7WM,QO8WfD,CAAA,IAAuC,oBAAZA,CAAA,KACpBgB,CAAA,CAAQhB,CAAA,IAClBA,CAAA,CAAS6E,IAAA,CAAK,UAAA7E,CAAA;IACb4E,CAAA,CAAa5E,CAAA,EAAOC,CAAA,CACrB;EAAA,KAEAA,CAAA,CAAIqD,IAAA,CAAKtD,CAAA,IAEHC,CACR;AAAA;AASA,SAASsE,EACRvE,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EAJD,IAmCMC,CAAA;IACAC,CAAA;IA9BCC,CAAA,GAAMN,CAAA,CAAWgC,GAAA;IACjBzB,CAAA,GAAOP,CAAA,CAAW8B,IAAA;IACpBtB,CAAA,GAAWP,CAAA,CAAYC,CAAA;EAkB3B,IP1ZmB,SO2ZjBM,CAAA,IAAuC,QAAlBR,CAAA,CAAWgC,GAAA,IAChCxB,CAAA,IACAF,CAAA,IAAOE,CAAA,CAASwB,GAAA,IAChBzB,CAAA,IAAQC,CAAA,CAASsB,IAAA,IACc,MPxaX,IOwanBtB,CAAA,CAAQkC,GAAA,GAEV,OAAOxC,CAAA;EAAA,IAVPC,CAAA,IPvZkB,QOwZjBK,CAAA,IAAmD,MPja/B,IOiaCA,CAAA,CAAQkC,GAAA,IAA0B,IAAI,IAa5D,KAFItC,CAAA,GAAIF,CAAA,GAAc,GAClBG,CAAA,GAAIH,CAAA,GAAc,GACfE,CAAA,IAAK,KAAKC,CAAA,GAAIJ,CAAA,CAAYwB,MAAA,GAAQ;IACxC,IAAIrB,CAAA,IAAK,GAAG;MAEX,KADAI,CAAA,GAAWP,CAAA,CAAYG,CAAA,MAGS,MPnbb,IOmbjBI,CAAA,CAAQkC,GAAA,KACTpC,CAAA,IAAOE,CAAA,CAASwB,GAAA,IAChBzB,CAAA,IAAQC,CAAA,CAASsB,IAAA,EAEjB,OAAO1B,CAAA;MAERA,CAAA,EACD;IAAA;IAEA,IAAIC,CAAA,GAAIJ,CAAA,CAAYwB,MAAA,EAAQ;MAE3B,KADAjB,CAAA,GAAWP,CAAA,CAAYI,CAAA,MAGS,MPhcb,IOgcjBG,CAAA,CAAQkC,GAAA,KACTpC,CAAA,IAAOE,CAAA,CAASwB,GAAA,IAChBzB,CAAA,IAAQC,CAAA,CAASsB,IAAA,EAEjB,OAAOzB,CAAA;MAERA,CAAA,EACD;IAAA;EACD;EAGD,QAAQ,CACT;AAAA;AFhdA,SAASyE,EAAS9E,CAAA,EAAOC,CAAA,EAAKC,CAAA;EACf,OAAVD,CAAA,CAAI,KACPD,CAAA,CAAM+E,WAAA,CAAY9E,CAAA,ELWA,QKXKC,CAAA,GAAgB,KAAKA,CAAA,IAE5CF,CAAA,CAAMC,CAAA,ILSY,QKVRC,CAAA,GACG,KACa,mBAATA,CAAA,IAAqBa,CAAA,CAAmBiE,IAAA,CAAK/E,CAAA,IACjDC,CAAA,GAEAA,CAAA,GAAQ,IAEvB;AAAA;AAyBgB,SAAA+E,EAAYjF,CAAA,EAAKC,CAAA,EAAMC,CAAA,EAAOC,CAAA,EAAUC,CAAA;EAAxC,IACXC,CAAA,EA8BGC,CAAA;EA5BPN,CAAA,EAAG,IAAY,WAARC,CAAA;IACN,IAAoB,mBAATC,CAAA,EACVF,CAAA,CAAIkF,KAAA,CAAMC,OAAA,GAAUjF,CAAA,MACd;MAKN,IAJuB,mBAAZC,CAAA,KACVH,CAAA,CAAIkF,KAAA,CAAMC,OAAA,GAAUhF,CAAA,GAAW,KAG5BA,CAAA,EACH,KAAKF,CAAA,IAAQE,CAAA,EACND,CAAA,IAASD,CAAA,IAAQC,CAAA,IACtB4E,CAAA,CAAS9E,CAAA,CAAIkF,KAAA,EAAOjF,CAAA,EAAM;MAK7B,IAAIC,CAAA,EACH,KAAKD,CAAA,IAAQC,CAAA,EACPC,CAAA,IAAYD,CAAA,CAAMD,CAAA,KAASE,CAAA,CAASF,CAAA,KACxC6E,CAAA,CAAS9E,CAAA,CAAIkF,KAAA,EAAOjF,CAAA,EAAMC,CAAA,CAAMD,CAAA,EAIpC;IAAA;EAAA,OAGI,IAAe,OAAXA,CAAA,CAAK,MAAwB,OAAXA,CAAA,CAAK,IAC/BI,CAAA,GAAaJ,CAAA,KAASA,CAAA,GAAOA,CAAA,CAAKmF,OAAA,CAAQ5E,CAAA,EAAe,QACnDF,CAAA,GAAgBL,CAAA,CAAKoF,WAAA,IAI1BpF,CAAA,GADGK,CAAA,IAAiBN,CAAA,IAAe,gBAARC,CAAA,IAAgC,eAARA,CAAA,GAC5CK,CAAA,CAAcgF,KAAA,CAAM,KAChBrF,CAAA,CAAKqF,KAAA,CAAM,IAElBtF,CAAA,CAAGC,CAAA,KAAaD,CAAA,CAAGC,CAAA,GAAc,CAAE,IACxCD,CAAA,CAAGC,CAAA,CAAYA,CAAA,GAAOI,CAAA,IAAcH,CAAA,EAEhCA,CAAA,GACEC,CAAA,GAQJD,CAAA,CAAMA,CAAA,GAAYC,CAAA,CAASD,CAAA,IAP3BA,CAAA,CAAMA,CAAA,GAAYO,CAAA,EAClBT,CAAA,CAAIuF,gBAAA,CACHtF,CAAA,EACAI,CAAA,GAAaM,CAAA,GAAoBD,CAAA,EACjCL,CAAA,KAMFL,CAAA,CAAIwF,mBAAA,CACHvF,CAAA,EACAI,CAAA,GAAaM,CAAA,GAAoBD,CAAA,EACjCL,CAAA,OAGI;IACN,ILtF2B,gCKsFvBD,CAAA,EAIHH,CAAA,GAAOA,CAAA,CAAKmF,OAAA,CAAQ,eAAe,KAAKA,OAAA,CAAQ,UAAU,UAE1D,IAAQ,WAARnF,CAAA,IACQ,YAARA,CAAA,IACQ,UAARA,CAAA,IACQ,UAARA,CAAA,IACQ,UAARA,CAAA,IAGQ,cAARA,CAAA,IACQ,cAARA,CAAA,IACQ,aAARA,CAAA,IACQ,aAARA,CAAA,IACQ,UAARA,CAAA,IACQ,aAARA,CAAA,IACAA,CAAA,IAAQD,CAAA,EAER;MACCA,CAAA,CAAIC,CAAA,ILxGY,QKwGJC,CAAA,GAAgB,KAAKA,CAAA;MAEjC,MAAMF,CAER;IAAA,CADG,QAAOA,CAAA,GACV;IASoB,qBAATE,CAAA,KLrHO,QKuHPA,CAAA,KAA4B,MAAVA,CAAA,IAA8B,OAAXD,CAAA,CAAK,KAGpDD,CAAA,CAAIyF,eAAA,CAAgBxF,CAAA,IAFpBD,CAAA,CAAI0F,YAAA,CAAazF,CAAA,EAAc,aAARA,CAAA,IAA8B,KAATC,CAAA,GAAgB,KAAKA,CAAA,EAInE;EAAA;AACD;AAOA,SAASyF,EAAiB3F,CAAA;EAMzB,iBAAiBE,CAAA;IAChB,IAAI,KAAID,CAAA,EAAa;MACpB,IAAME,CAAA,GAAe,KAAIF,CAAA,CAAYC,CAAA,CAAE4B,IAAA,GAAO9B,CAAA;MAC9C,IL7IiB,QK6IbE,CAAA,CAAEC,CAAA,EACLD,CAAA,CAAEC,CAAA,GAAcM,CAAA,QAKV,IAAIP,CAAA,CAAEC,CAAA,GAAcA,CAAA,CAAaD,CAAA,EACvC;MAED,OAAOC,CAAA,CAAaF,CAAA,CAAQ2F,KAAA,GAAQ3F,CAAA,CAAQ2F,KAAA,CAAM1F,CAAA,IAAKA,CAAA,CACxD;IAAA;EACD,CACD;AAAA;AAAA,SGzHgB2D,EACf7D,CAAA,EACAE,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EAAA,IAGIC,CAAA;IAkBEC,CAAA;IAAGC,CAAA;IAAOC,CAAA;IAAUC,CAAA;IAAUQ,CAAA;IAAUM,CAAA;IACxCe,CAAA;IACEK,CAAA;IAMFC,CAAA;IACAE,CAAA;IAyGOG,CAAA;IA4BPW,CAAA;IACHE,CAAA;IASSQ,CAAA;IA6BNL,CAAA;IAgDOO,CAAA;IAtPZG,CAAA,GAAU/E,CAAA,CAAS4B,IAAA;EAIpB,IRjDwB,QQiDpB5B,CAAA,CAASqC,WAAA,EAA0B,ORlDpB;EAbU,MQkEzBpC,CAAA,CAAQuC,GAAA,KACXjC,CAAA,MRrE0B,KQqETN,CAAA,CAAQuC,GAAA,GAEzBpC,CAAA,GAAoB,CADpBE,CAAA,GAASN,CAAA,CAAQmC,GAAA,GAAQlC,CAAA,CAAQkC,GAAA,KAI7B1B,CAAA,GAAMV,CAAA,CAAOmC,GAAA,KAASzB,CAAA,CAAIT,CAAA;EAE/BF,CAAA,EAAO,IAAsB,qBAAXiF,CAAA,EACjB;IAkEC,IAhEIrC,CAAA,GAAW1C,CAAA,CAAS6B,KAAA,EAClBkB,CAAA,GACL,eAAegC,CAAA,IAAWA,CAAA,CAAQY,SAAA,CAAUC,MAAA,EAKzC5C,CAAA,IADJvC,CAAA,GAAMsE,CAAA,CAAQc,WAAA,KACQ3F,CAAA,CAAcO,CAAA,CAAG2B,GAAA,GACnCc,CAAA,GAAmBzC,CAAA,GACpBuC,CAAA,GACCA,CAAA,CAASnB,KAAA,CAAMiE,KAAA,GACfrF,CAAA,CAAGwB,EAAA,GACJ/B,CAAA,EAGCD,CAAA,CAAQmC,GAAA,GAEXT,CAAA,IADAjB,CAAA,GAAIV,CAAA,CAAQoC,GAAA,GAAcnC,CAAA,CAAQmC,GAAA,EACNH,EAAA,GAAwBvB,CAAA,CAACqF,GAAA,IAGjDhD,CAAA,GAEH/C,CAAA,CAAQoC,GAAA,GAAc1B,CAAA,GAAI,IAAIqE,CAAA,CAAQrC,CAAA,EAAUQ,CAAA,KAGhDlD,CAAA,CAAQoC,GAAA,GAAc1B,CAAA,GAAI,IAAImC,CAAA,CAC7BH,CAAA,EACAQ,CAAA,GAEDxC,CAAA,CAAE2B,WAAA,GAAc0C,CAAA,EAChBrE,CAAA,CAAEkF,MAAA,GAASI,CAAA,GAERhD,CAAA,IAAUA,CAAA,CAASiD,GAAA,CAAIvF,CAAA,GAE3BA,CAAA,CAAEmB,KAAA,GAAQa,CAAA,EACLhC,CAAA,CAAEwF,KAAA,KAAOxF,CAAA,CAAEwF,KAAA,GAAQ,KACxBxF,CAAA,CAAEoC,OAAA,GAAUI,CAAA,EACZxC,CAAA,CAACkD,GAAA,GAAkB1D,CAAA,EACnBS,CAAA,GAAQD,CAAA,CAACyC,GAAA,IAAU,GACnBzC,CAAA,CAACyF,GAAA,GAAoB,IACrBzF,CAAA,CAAC0F,GAAA,GAAmB,KAIjBrD,CAAA,IR5Ga,QQ4GOrC,CAAA,CAAC2F,GAAA,KACxB3F,CAAA,CAAC2F,GAAA,GAAc3F,CAAA,CAAEwF,KAAA,GAGdnD,CAAA,IRhHa,QQgHOgC,CAAA,CAAQuB,wBAAA,KAC3B5F,CAAA,CAAC2F,GAAA,IAAe3F,CAAA,CAAEwF,KAAA,KACrBxF,CAAA,CAAC2F,GAAA,GAAcpF,CAAA,CAAO,IAAIP,CAAA,CAAC2F,GAAA,IAG5BpF,CAAA,CACCP,CAAA,CAAC2F,GAAA,EACDtB,CAAA,CAAQuB,wBAAA,CAAyB5D,CAAA,EAAUhC,CAAA,CAAC2F,GAAA,KAI9CzF,CAAA,GAAWF,CAAA,CAAEmB,KAAA,EACbhB,CAAA,GAAWH,CAAA,CAAEwF,KAAA,EACbxF,CAAA,CAAC4B,GAAA,GAAUtC,CAAA,EAGPW,CAAA,EAEFoC,CAAA,IRlIe,QQmIfgC,CAAA,CAAQuB,wBAAA,IRnIO,QQoIf5F,CAAA,CAAE6F,kBAAA,IAEF7F,CAAA,CAAE6F,kBAAA,IAGCxD,CAAA,IRzIY,QQyIQrC,CAAA,CAAE8F,iBAAA,IACzB9F,CAAA,CAACyF,GAAA,CAAkB/C,IAAA,CAAK1C,CAAA,CAAE8F,iBAAA,OAErB;MAUN,IARCzD,CAAA,IR9Ie,QQ+IfgC,CAAA,CAAQuB,wBAAA,IACR5D,CAAA,KAAa9B,CAAA,IRhJE,QQiJfF,CAAA,CAAE+F,yBAAA,IAEF/F,CAAA,CAAE+F,yBAAA,CAA0B/D,CAAA,EAAUQ,CAAA,IAIpCxC,CAAA,CAACyB,GAAA,IRvJY,QQwJdzB,CAAA,CAAEgG,qBAAA,KAKI,MAJNhG,CAAA,CAAEgG,qBAAA,CACDhE,CAAA,EACAhC,CAAA,CAAC2F,GAAA,EACDnD,CAAA,KAEFlD,CAAA,CAAQsC,GAAA,IAAcrC,CAAA,CAAQqC,GAAA,EAC7B;QAkBD,KAhBItC,CAAA,CAAQsC,GAAA,IAAcrC,CAAA,CAAQqC,GAAA,KAKjC5B,CAAA,CAAEmB,KAAA,GAAQa,CAAA,EACVhC,CAAA,CAAEwF,KAAA,GAAQxF,CAAA,CAAC2F,GAAA,EACX3F,CAAA,CAACyC,GAAA,IAAU,IAGZnD,CAAA,CAAQmC,GAAA,GAAQlC,CAAA,CAAQkC,GAAA,EACxBnC,CAAA,CAAQgC,GAAA,GAAa/B,CAAA,CAAQ+B,GAAA,EAC7BhC,CAAA,CAAQgC,GAAA,CAAW2C,IAAA,CAAK,UAAA7E,CAAA;UACnBA,CAAA,KAAOA,CAAA,CAAKmC,EAAA,GAAWjC,CAAA,CAC5B;QAAA,IAESqD,CAAA,GAAI,GAAGA,CAAA,GAAI3C,CAAA,CAAC0F,GAAA,CAAiB7E,MAAA,EAAQ8B,CAAA,IAC7C3C,CAAA,CAACyF,GAAA,CAAkB/C,IAAA,CAAK1C,CAAA,CAAC0F,GAAA,CAAiB/C,CAAA;QAE3C3C,CAAA,CAAC0F,GAAA,GAAmB,IAEhB1F,CAAA,CAACyF,GAAA,CAAkB5E,MAAA,IACtBlB,CAAA,CAAY+C,IAAA,CAAK1C,CAAA;QAGlB,MAAMZ,CACP;MAAA;MR3LgB,QQ6LZY,CAAA,CAAEiG,mBAAA,IACLjG,CAAA,CAAEiG,mBAAA,CAAoBjE,CAAA,EAAUhC,CAAA,CAAC2F,GAAA,EAAanD,CAAA,GAG3CH,CAAA,IRjMY,QQiMQrC,CAAA,CAAEkG,kBAAA,IACzBlG,CAAA,CAACyF,GAAA,CAAkB/C,IAAA,CAAK;QACvB1C,CAAA,CAAEkG,kBAAA,CAAmBhG,CAAA,EAAUC,CAAA,EAAUQ,CAAA,CAC1C;MAAA,EAEF;IAAA;IASA,IAPAX,CAAA,CAAEoC,OAAA,GAAUI,CAAA,EACZxC,CAAA,CAAEmB,KAAA,GAAQa,CAAA,EACVhC,CAAA,CAACgD,GAAA,GAAc5D,CAAA,EACfY,CAAA,CAACyB,GAAA,IAAU,GAEP6B,CAAA,GAAajE,CAAA,CAAOuD,GAAA,EACvBY,CAAA,GAAQ,GACLnB,CAAA,EAAkB;MAQrB,KAPArC,CAAA,CAAEwF,KAAA,GAAQxF,CAAA,CAAC2F,GAAA,EACX3F,CAAA,CAACyC,GAAA,IAAU,GAEPa,CAAA,IAAYA,CAAA,CAAWhE,CAAA,GAE3BS,CAAA,GAAMC,CAAA,CAAEkF,MAAA,CAAOlF,CAAA,CAAEmB,KAAA,EAAOnB,CAAA,CAAEwF,KAAA,EAAOxF,CAAA,CAAEoC,OAAA,GAE1B4B,CAAA,GAAI,GAAGA,CAAA,GAAIhE,CAAA,CAAC0F,GAAA,CAAiB7E,MAAA,EAAQmD,CAAA,IAC7ChE,CAAA,CAACyF,GAAA,CAAkB/C,IAAA,CAAK1C,CAAA,CAAC0F,GAAA,CAAiB1B,CAAA;MAE3ChE,CAAA,CAAC0F,GAAA,GAAmB,EACrB;IAAA,OACC;MACC1F,CAAA,CAACyC,GAAA,IAAU,GACPa,CAAA,IAAYA,CAAA,CAAWhE,CAAA,GAE3BS,CAAA,GAAMC,CAAA,CAAEkF,MAAA,CAAOlF,CAAA,CAAEmB,KAAA,EAAOnB,CAAA,CAAEwF,KAAA,EAAOxF,CAAA,CAAEoC,OAAA,GAGnCpC,CAAA,CAAEwF,KAAA,GAAQxF,CAAA,CAAC2F,GAAA;IAAA,SACH3F,CAAA,CAACyC,GAAA,MAAae,CAAA,GAAQ;IAIhCxD,CAAA,CAAEwF,KAAA,GAAQxF,CAAA,CAAC2F,GAAA,ERxOM,QQ0Ob3F,CAAA,CAAEmG,eAAA,KACL3G,CAAA,GAAgBe,CAAA,CAAOA,CAAA,CAAO,CAAE,GAAEf,CAAA,GAAgBQ,CAAA,CAAEmG,eAAA,MAGjD9D,CAAA,KAAqBpC,CAAA,IR9OR,QQ8OiBD,CAAA,CAAEoG,uBAAA,KACnCzF,CAAA,GAAWX,CAAA,CAAEoG,uBAAA,CAAwBlG,CAAA,EAAUC,CAAA,IAK5CwD,CAAA,GAAe5D,CAAA,ERpPF,QQmPhBA,CAAA,IAAeA,CAAA,CAAImB,IAAA,KAASgB,CAAA,IRnPZ,QQmPwBnC,CAAA,CAAIqB,GAAA,KAI5CuC,CAAA,GAAe0C,CAAA,CAAUtG,CAAA,CAAIoB,KAAA,CAAML,QAAA,IAGpClB,CAAA,GAASyD,CAAA,CACRjE,CAAA,EACAgB,CAAA,CAAQuD,CAAA,IAAgBA,CAAA,GAAe,CAACA,CAAA,GACxCrE,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,GAGDE,CAAA,CAAEuC,IAAA,GAAOjD,CAAA,CAAQmC,GAAA,EAGjBnC,CAAA,CAAQwC,GAAA,KRjRe,KQmRnB9B,CAAA,CAACyF,GAAA,CAAkB5E,MAAA,IACtBlB,CAAA,CAAY+C,IAAA,CAAK1C,CAAA,GAGdiB,CAAA,KACHjB,CAAA,CAACqF,GAAA,GAAiBrF,CAAA,CAACuB,EAAA,GRlRH,KQ6SlB;EAAA,CAzBE,QAAOnC,CAAA;IAGR,IAFAE,CAAA,CAAQsC,GAAA,GRrRS,MQuRb/B,CAAA,IRvRa,QQuREH,CAAA;MAClB,IAAIN,CAAA,CAAEkH,IAAA,EAAM;QAKX,KAJAhH,CAAA,CAAQwC,GAAA,IAAWjC,CAAA,GAChB,MRvSsB,KQ0SlBD,CAAA,IAA6B,KAAnBA,CAAA,CAAOmE,QAAA,IAAiBnE,CAAA,CAAO6D,WAAA,GAC/C7D,CAAA,GAASA,CAAA,CAAO6D,WAAA;QAGjB/D,CAAA,CAAkBA,CAAA,CAAkB6G,OAAA,CAAQ3G,CAAA,KRjS7B,MQkSfN,CAAA,CAAQmC,GAAA,GAAQ7B,CACjB;MAAA,OACC,KAASsE,CAAA,GAAIxE,CAAA,CAAkBmB,MAAA,EAAQqD,CAAA,KACtC1D,CAAA,CAAWd,CAAA,CAAkBwE,CAAA;IAAA,OAI/B5E,CAAA,CAAQmC,GAAA,GAAQlC,CAAA,CAAQkC,GAAA,EACxBnC,CAAA,CAAQgC,GAAA,GAAa/B,CAAA,CAAQ+B,GAAA;IAE9BjC,CAAA,CAAOoC,GAAA,CAAarC,CAAA,EAAGE,CAAA,EAAUC,CAAA,CAClC;EAAA,OR7SkB,QQ+SlBG,CAAA,IACAJ,CAAA,CAAQsC,GAAA,IAAcrC,CAAA,CAAQqC,GAAA,IAE9BtC,CAAA,CAAQgC,GAAA,GAAa/B,CAAA,CAAQ+B,GAAA,EAC7BhC,CAAA,CAAQmC,GAAA,GAAQlC,CAAA,CAAQkC,GAAA,IAExB7B,CAAA,GAASN,CAAA,CAAQmC,GAAA,GAAQ+E,CAAA,CACxBjH,CAAA,CAAQkC,GAAA,EACRnC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAE,CAAA,EACAC,CAAA;EAMF,QAFKC,CAAA,GAAMV,CAAA,CAAQoH,MAAA,KAAS1G,CAAA,CAAIT,CAAA,GR/UH,MQiVtBA,CAAA,CAAQwC,GAAA,QAA2B,IAAYlC,CACvD;AAAA;AAAA,SAOgBwD,EAAWhE,CAAA,EAAaE,CAAA,EAAMC,CAAA;EAC7C,KAAK,IAAIC,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,CAASsB,MAAA,EAAQrB,CAAA,IACpC+D,CAAA,CAAShE,CAAA,CAASC,CAAA,GAAID,CAAA,GAAWC,CAAA,GAAID,CAAA,GAAWC,CAAA;EAG7CH,CAAA,CAAOqC,GAAA,IAAUrC,CAAA,CAAOqC,GAAA,CAASpC,CAAA,EAAMF,CAAA,GAE3CA,CAAA,CAAY6E,IAAA,CAAK,UAAA3E,CAAA;IAChB;MAECF,CAAA,GAAcE,CAAA,CAACmG,GAAA,EACfnG,CAAA,CAACmG,GAAA,GAAoB,IACrBrG,CAAA,CAAY6E,IAAA,CAAK,UAAA7E,CAAA;QAEhBA,CAAA,CAAG2B,IAAA,CAAKzB,CAAA,CACT;MAAA,EAGD;IAAA,CAFE,QAAOF,CAAA;MACRC,CAAA,CAAOoC,GAAA,CAAarC,CAAA,EAAGE,CAAA,CAACsC,GAAA,CACzB;IAAA;EACD,EACD;AAAA;AAEA,SAASyE,EAAUjH,CAAA;EAClB,OACgB,mBAARA,CAAA,IRpWW,QQqWlBA,CAAA,IACCA,CAAA,CAAIoC,GAAA,IAAWpC,CAAA,CAAIoC,GAAA,GAAU,IAEvBpC,CAAA,GAGJgB,CAAA,CAAQhB,CAAA,IACJA,CAAA,CAAKsH,GAAA,CAAIL,CAAA,IAGV9F,CAAA,CAAO,CAAE,GAAEnB,CAAA,CACnB;AAAA;AAiBA,SAASoH,EACRlH,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;EATD,IAeKC,CAAA;IAEAC,CAAA;IAEAE,CAAA;IAEAC,CAAA;IACAI,CAAA;IACAI,CAAA;IACAM,CAAA;IAbAe,CAAA,GAAWxC,CAAA,CAAS2B,KAAA;IACpBe,CAAA,GAAW3C,CAAA,CAAS4B,KAAA;IACpBgB,CAAA,GAAkC5C,CAAA,CAAS2B,IAAA;EAkB/C,IAJgB,SAAZiB,CAAA,GAAmBzC,CAAA,GRhaK,+BQiaP,UAAZyC,CAAA,GAAoBzC,CAAA,GR/ZA,uCQganBA,CAAA,KAAWA,CAAA,GRjaS,iCAGX,QQgafC,CAAA,EACH,KAAKI,CAAA,GAAI,GAAGA,CAAA,GAAIJ,CAAA,CAAkBkB,MAAA,EAAQd,CAAA,IAMzC,KALAQ,CAAA,GAAQZ,CAAA,CAAkBI,CAAA,MAOzB,kBAAkBQ,CAAA,MAAW4B,CAAA,KAC5BA,CAAA,GAAW5B,CAAA,CAAMoG,SAAA,IAAaxE,CAAA,GAA6B,KAAlB5B,CAAA,CAAMwD,QAAA,GAC/C;IACDzE,CAAA,GAAMiB,CAAA,EACNZ,CAAA,CAAkBI,CAAA,IR7aF;IQ8ahB;EACD;EAIF,IRnbmB,QQmbfT,CAAA,EAAa;IAChB,IRpbkB,QQobd6C,CAAA,EACH,OAAOyE,QAAA,CAASC,cAAA,CAAe3E,CAAA;IAGhC5C,CAAA,GAAMsH,QAAA,CAASE,eAAA,CACdpH,CAAA,EACAyC,CAAA,EACAD,CAAA,CAAS6E,EAAA,IAAM7E,CAAA,GAKZrC,CAAA,KACCR,CAAA,CAAO2H,GAAA,IACV3H,CAAA,CAAO2H,GAAA,CAAoBzH,CAAA,EAAUI,CAAA,GACtCE,CAAA,IAAc,IAGfF,CAAA,GRtckB,IQucnB;EAAA;EAEA,IRzcmB,QQycfwC,CAAA,EAECH,CAAA,KAAaE,CAAA,IAAcrC,CAAA,IAAeP,CAAA,CAAI2H,IAAA,IAAQ/E,CAAA,KACzD5C,CAAA,CAAI2H,IAAA,GAAO/E,CAAA,OAEN;IASN,IAPAvC,CAAA,GAAoBA,CAAA,IAAqBP,CAAA,CAAM2B,IAAA,CAAKzB,CAAA,CAAI4H,UAAA,GAExDlF,CAAA,GAAWxC,CAAA,CAAS2B,KAAA,IAASlB,CAAA,GAKxBJ,CAAA,IRvda,QQudEF,CAAA,EAEnB,KADAqC,CAAA,GAAW,IACNjC,CAAA,GAAI,GAAGA,CAAA,GAAIT,CAAA,CAAI6H,UAAA,CAAWtG,MAAA,EAAQd,CAAA,IAEtCiC,CAAA,EADAzB,CAAA,GAAQjB,CAAA,CAAI6H,UAAA,CAAWpH,CAAA,GACRqH,IAAA,IAAQ7G,CAAA,CAAM6E,KAAA;IAI/B,KAAKrF,CAAA,IAAKiC,CAAA,EAET,IADAzB,CAAA,GAAQyB,CAAA,CAASjC,CAAA,GACR,cAALA,CAAA,QACO,IAAK,6BAALA,CAAA,EACVG,CAAA,GAAUK,CAAA,MACJ,MAAMR,CAAA,IAAKmC,CAAA,GAAW;MAC5B,IACO,WAALnC,CAAA,IAAgB,kBAAkBmC,CAAA,IAC7B,aAALnC,CAAA,IAAkB,oBAAoBmC,CAAA,EAEvC;MAEDmC,CAAA,CAAY/E,CAAA,EAAKS,CAAA,ER3eD,MQ2eUQ,CAAA,EAAOb,CAAA,CAClC;IAAA;IAKD,KAAKK,CAAA,IAAKmC,CAAA,EACT3B,CAAA,GAAQ2B,CAAA,CAASnC,CAAA,GACR,cAALA,CAAA,GACHI,CAAA,GAAcI,CAAA,GACC,6BAALR,CAAA,GACVC,CAAA,GAAUO,CAAA,GACK,WAALR,CAAA,GACVY,CAAA,GAAaJ,CAAA,GACE,aAALR,CAAA,GACVkB,CAAA,GAAUV,CAAA,GAERV,CAAA,IAA+B,qBAATU,CAAA,IACxByB,CAAA,CAASjC,CAAA,MAAOQ,CAAA,IAEhB8D,CAAA,CAAY/E,CAAA,EAAKS,CAAA,EAAGQ,CAAA,EAAOyB,CAAA,CAASjC,CAAA,GAAIL,CAAA;IAK1C,IAAIM,CAAA,EAGDH,CAAA,IACCK,CAAA,KACAF,CAAA,CAAOqH,MAAA,IAAWnH,CAAA,CAAOmH,MAAA,IAAWrH,CAAA,CAAOqH,MAAA,IAAW/H,CAAA,CAAIgI,SAAA,MAE5DhI,CAAA,CAAIgI,SAAA,GAAYtH,CAAA,CAAOqH,MAAA,GAGxB9H,CAAA,CAAQ+B,GAAA,GAAa,QAsBrB,IApBIpB,CAAA,KAASZ,CAAA,CAAIgI,SAAA,GAAY,KAE7BjE,CAAA,CAEkB,cAAjB9D,CAAA,CAAS2B,IAAA,GAAqB5B,CAAA,CAAIiI,OAAA,GAAUjI,CAAA,EAC5Cc,CAAA,CAAQD,CAAA,IAAeA,CAAA,GAAc,CAACA,CAAA,GACtCZ,CAAA,EACAC,CAAA,EACAC,CAAA,EACY,mBAAZ0C,CAAA,GR5hB2B,iCQ4hBqBzC,CAAA,EAChDC,CAAA,EACAC,CAAA,EACAD,CAAA,GACGA,CAAA,CAAkB,KAClBH,CAAA,CAAQ8B,GAAA,IAAce,CAAA,CAAc7C,CAAA,EAAU,IACjDK,CAAA,EACAC,CAAA,GRhiBgB,QQoiBbH,CAAA,EACH,KAAKI,CAAA,GAAIJ,CAAA,CAAkBkB,MAAA,EAAQd,CAAA,KAClCS,CAAA,CAAWb,CAAA,CAAkBI,CAAA;IAM3BF,CAAA,KACJE,CAAA,GAAI,SACY,cAAZoC,CAAA,IR9iBa,QQ8iBaxB,CAAA,GAC7BrB,CAAA,CAAIuF,eAAA,CAAgB,WR9iBC,QQgjBrBlE,CAAA,KAKCA,CAAA,KAAerB,CAAA,CAAIS,CAAA,KACN,cAAZoC,CAAA,KAA2BxB,CAAA,IAIf,YAAZwB,CAAA,IAAwBxB,CAAA,IAAcqB,CAAA,CAASjC,CAAA,MAEjDsE,CAAA,CAAY/E,CAAA,EAAKS,CAAA,EAAGY,CAAA,EAAYqB,CAAA,CAASjC,CAAA,GAAIL,CAAA,GAG9CK,CAAA,GAAI,WR/jBkB,QQgkBlBkB,CAAA,IAAwBA,CAAA,IAAW3B,CAAA,CAAIS,CAAA,KAC1CsE,CAAA,CAAY/E,CAAA,EAAKS,CAAA,EAAGkB,CAAA,EAASe,CAAA,CAASjC,CAAA,GAAIL,CAAA,EAG7C;EAAA;EAEA,OAAOJ,CACR;AAAA;AAQgB,SAAAiE,EAASnE,CAAA,EAAKE,CAAA,EAAOC,CAAA;EACpC;IACC,IAAkB,qBAAPH,CAAA,EAAmB;MAC7B,IAAII,CAAA,GAAuC,qBAAhBJ,CAAA,CAAG0C,GAAA;MAC1BtC,CAAA,IAEHJ,CAAA,CAAG0C,GAAA,IAGCtC,CAAA,IRzlBY,QQylBKF,CAAA,KAIrBF,CAAA,CAAG0C,GAAA,GAAY1C,CAAA,CAAIE,CAAA,EAErB;IAAA,OAAOF,CAAA,CAAI6C,OAAA,GAAU3C,CAGtB;EAAA,CAFE,QAAOF,CAAA;IACRC,CAAA,CAAOoC,GAAA,CAAarC,CAAA,EAAGG,CAAA,CACxB;EAAA;AACD;AASgB,SAAAqE,EAAQxE,CAAA,EAAOE,CAAA,EAAaC,CAAA;EAA5B,IACXC,CAAA,EAsBMC,CAAA;EAbV,IARIJ,CAAA,CAAQmI,OAAA,IAASnI,CAAA,CAAQmI,OAAA,CAAQpI,CAAA,IAEhCI,CAAA,GAAIJ,CAAA,CAAMiC,GAAA,MACT7B,CAAA,CAAEyC,OAAA,IAAWzC,CAAA,CAAEyC,OAAA,IAAW7C,CAAA,CAAKqC,GAAA,IACnC8B,CAAA,CAAS/D,CAAA,ERlnBQ,MQknBCF,CAAA,IRlnBD,SQsnBdE,CAAA,GAAIJ,CAAA,CAAKsC,GAAA,GAAsB;IACnC,IAAIlC,CAAA,CAAEiI,oBAAA,EACL;MACCjI,CAAA,CAAEiI,oBAAA,EAGH;IAAA,CAFE,QAAOrI,CAAA;MACRC,CAAA,CAAOoC,GAAA,CAAarC,CAAA,EAAGE,CAAA,CACxB;IAAA;IAGDE,CAAA,CAAE+C,IAAA,GAAO/C,CAAA,CAACwD,GAAA,GR/nBQ,IQgoBnB;EAAA;EAEA,IAAKxD,CAAA,GAAIJ,CAAA,CAAKkC,GAAA,EACb,KAAS7B,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,CAAEqB,MAAA,EAAQpB,CAAA,IACzBD,CAAA,CAAEC,CAAA,KACLmE,CAAA,CACCpE,CAAA,CAAEC,CAAA,GACFH,CAAA,EACAC,CAAA,IAAmC,qBAAdH,CAAA,CAAM8B,IAAA;EAM1B3B,CAAA,IACJiB,CAAA,CAAWpB,CAAA,CAAKqC,GAAA,GAGjBrC,CAAA,CAAKsC,GAAA,GAActC,CAAA,CAAKmC,EAAA,GAAWnC,CAAA,CAAKqC,GAAA,QRjpBhB,CQkpBzB;AAAA;AAGA,SAAS6D,EAASlG,CAAA,EAAOC,CAAA,EAAOC,CAAA;EAC/B,YAAYqC,WAAA,CAAYvC,CAAA,EAAOE,CAAA,CAChC;AAAA;AC3pBO,SAASoI,EAAOpI,CAAA,EAAOC,CAAA,EAAWC,CAAA;EAAlC,IAWFC,CAAA,EAOAC,CAAA,EAQAC,CAAA,EACHC,CAAA;EAzBGL,CAAA,IAAaqH,QAAA,KAChBrH,CAAA,GAAYqH,QAAA,CAASe,eAAA,GAGlBtI,CAAA,CAAOkC,EAAA,IAAQlC,CAAA,CAAOkC,EAAA,CAAOjC,CAAA,EAAOC,CAAA,GAYpCG,CAAA,IAPAD,CAAA,GAAoC,qBAAfD,CAAA,ITRN,OSiBfA,CAAA,IAAeA,CAAA,CAAW8B,GAAA,IAAe/B,CAAA,CAAS+B,GAAA,EAMlD3B,CAAA,GAAc,IACjBC,CAAA,GAAW,IACZqD,CAAA,CACC1D,CAAA,EAPDD,CAAA,KAAWG,CAAA,IAAeD,CAAA,IAAgBD,CAAA,EAAS+B,GAAA,GAClDX,CAAA,CAAcuB,CAAA,ETpBI,MSoBY,CAAC5C,CAAA,IAU/BI,CAAA,IAAYO,CAAA,EACZA,CAAA,EACAV,CAAA,CAAU4D,YAAA,GACT1D,CAAA,IAAeD,CAAA,GACb,CAACA,CAAA,IACDE,CAAA,GTnCe,OSqCdH,CAAA,CAAUqI,UAAA,GACTxI,CAAA,CAAM2B,IAAA,CAAKxB,CAAA,CAAU2H,UAAA,ITtCR,MSwClBvH,CAAA,GACCF,CAAA,IAAeD,CAAA,GACbA,CAAA,GACAE,CAAA,GACCA,CAAA,CAAQ+B,GAAA,GACRlC,CAAA,CAAUqI,UAAA,EACdnI,CAAA,EACAG,CAAA,GAIDwD,CAAA,CAAWzD,CAAA,EAAaL,CAAA,EAAOM,CAAA,CAChC;AAAA;AAOO,SAASiI,EAAQzI,CAAA,EAAOC,CAAA;EAC9BqI,CAAA,CAAOtI,CAAA,EAAOC,CAAA,EAAWwI,CAAA,CAC1B;AAAA;AAAA,SChEgBC,EAAazI,CAAA,EAAOC,CAAA,EAAOC,CAAA;EAAA,IAEzCC,CAAA;IACAC,CAAA;IACAC,CAAA;IAEGC,CAAA;IALAC,CAAA,GAAkBW,CAAA,CAAO,CAAE,GAAElB,CAAA,CAAM8B,KAAA;EAWvC,KAAKzB,CAAA,IAJDL,CAAA,CAAM6B,IAAA,IAAQ7B,CAAA,CAAM6B,IAAA,CAAKF,YAAA,KAC5BrB,CAAA,GAAeN,CAAA,CAAM6B,IAAA,CAAKF,YAAA,GAGjB1B,CAAA,EACA,SAALI,CAAA,GAAYF,CAAA,GAAMF,CAAA,CAAMI,CAAA,IACd,SAALA,CAAA,GAAYD,CAAA,GAAMH,CAAA,CAAMI,CAAA,IAEhCE,CAAA,CAAgBF,CAAA,IVZM,QUWdJ,CAAA,CAAMI,CAAA,KVXQ,QUWWC,CAAA,GACZA,CAAA,CAAaD,CAAA,IAEbJ,CAAA,CAAMI,CAAA;EAS7B,OALIkB,SAAA,CAAUC,MAAA,GAAS,MACtBjB,CAAA,CAAgBkB,QAAA,GACfF,SAAA,CAAUC,MAAA,GAAS,IAAIzB,CAAA,CAAM2B,IAAA,CAAKH,SAAA,EAAW,KAAKrB,CAAA,GAG7C0B,CAAA,CACN5B,CAAA,CAAM6B,IAAA,EACNtB,CAAA,EACAJ,CAAA,IAAOH,CAAA,CAAM+B,GAAA,EACb3B,CAAA,IAAOJ,CAAA,CAAMgC,GAAA,EV5BK,KU+BpB;AAAA;AJ1CgB,SAAA0G,EAAc3I,CAAA;EAC7B,SAASC,EAAQD,CAAA;IAAjB,IAGME,CAAA,EACAC,CAAA;IA+BL,OAlCK,KAAK4G,eAAA,KAEL7G,CAAA,GAAO,IAAI0I,GAAA,KACXzI,CAAA,GAAM,CAAE,GACRF,CAAA,CAAOqC,GAAA,IAAQ,MAEnB,KAAKyE,eAAA,GAAkB;MAAM,OAAA5G,CAAG;IAAA,GAEhC,KAAKkI,oBAAA,GAAuB;MAC3BnI,CAAA,GNAgB,IMCjB;IAAA,GAEA,KAAK0G,qBAAA,GAAwB,UAAU5G,CAAA;MAElC,KAAK+B,KAAA,CAAMiE,KAAA,IAAShG,CAAA,CAAOgG,KAAA,IAC9B9F,CAAA,CAAK2I,OAAA,CAAQ,UAAA7I,CAAA;QACZA,CAAA,CAACqC,GAAA,IAAU,GACXe,CAAA,CAAcpD,CAAA,CACf;MAAA,EAEF;IAAA,GAEA,KAAKmG,GAAA,GAAM,UAAAnG,CAAA;MACVE,CAAA,CAAK4I,GAAA,CAAI9I,CAAA;MACT,IAAIC,CAAA,GAAMD,CAAA,CAAEqI,oBAAA;MACZrI,CAAA,CAAEqI,oBAAA,GAAuB;QACpBnI,CAAA,IACHA,CAAA,CAAK6I,MAAA,CAAO/I,CAAA,GAETC,CAAA,IAAKA,CAAA,CAAI0B,IAAA,CAAK3B,CAAA,CACnB;MAAA,CACD;IAAA,IAGMA,CAAA,CAAM0B,QACd;EAAA;EAgBA,OAdAzB,CAAA,CAAOqC,GAAA,GAAO,SAAS1B,CAAA,IACvBX,CAAA,CAAOkC,EAAA,GAAiBnC,CAAA,EAQxBC,CAAA,CAAQ+I,QAAA,GACP/I,CAAA,CAAOgJ,GAAA,IANRhJ,CAAA,CAAQiJ,QAAA,GAAW,UAAClJ,CAAA,EAAOC,CAAA;IAC1B,OAAOD,CAAA,CAAM0B,QAAA,CAASzB,CAAA,CACvB;EAAA,GAKkB8F,WAAA,GAChB9F,CAAA,EAEKA,CACR;AAAA;ALhCaD,CAAA,GAAQc,CAAA,CAAUwE,KAAA,EChBzBrF,CAAA,GAAU;EACfoC,GAAA,ESDM,SAAAA,CAAqBrC,CAAA,EAAOC,CAAA,EAAOC,CAAA,EAAUC,CAAA;IAQnD,KANA,IAAIC,CAAA,EAEHC,CAAA,EAEAC,CAAA,EAEOL,CAAA,GAAQA,CAAA,CAAKkC,EAAA,GACpB,KAAK/B,CAAA,GAAYH,CAAA,CAAKqC,GAAA,MAAiBlC,CAAA,CAAS+B,EAAA,EAC/C;MAcC,KAbA9B,CAAA,GAAOD,CAAA,CAAUmC,WAAA,KXND,QWQJlC,CAAA,CAAK8I,wBAAA,KAChB/I,CAAA,CAAUgJ,QAAA,CAAS/I,CAAA,CAAK8I,wBAAA,CAAyBnJ,CAAA,IACjDM,CAAA,GAAUF,CAAA,CAASiD,GAAA,GXVJ,QWaZjD,CAAA,CAAUiJ,iBAAA,KACbjJ,CAAA,CAAUiJ,iBAAA,CAAkBrJ,CAAA,EAAOG,CAAA,IAAa,CAAE,IAClDG,CAAA,GAAUF,CAAA,CAASiD,GAAA,GAIhB/C,CAAA,EACH,OAAQF,CAAA,CAAS6F,GAAA,GAAiB7F,CAIpC;IAAA,CAFE,QAAOH,CAAA;MACRD,CAAA,GAAQC,CACT;IAAA;IAIF,MAAMD,CACP;EAAA;AAAA,GRzCIE,CAAA,GAAU,GA2FDC,CAAA,GAAiB,SAAAmJ,CAAAtJ,CAAA;EAAK,OH/Ef,QGgFnBA,CAAA,IH/EwB,QG+EPA,CAAA,CAAMuC,WAAwB;AAAA,GCrEhDQ,CAAA,CAAc8C,SAAA,CAAUuD,QAAA,GAAW,UAAUpJ,CAAA,EAAQC,CAAA;EAEpD,IAAIC,CAAA;EAEHA,CAAA,GJfkB,QIcf,KAAIqG,GAAA,IAAuB,KAAIA,GAAA,IAAe,KAAKH,KAAA,GAClD,KAAIG,GAAA,GAEJ,KAAIA,GAAA,GAAcpF,CAAA,CAAO,CAAE,GAAE,KAAKiF,KAAA,GAGlB,qBAAVpG,CAAA,KAGVA,CAAA,GAASA,CAAA,CAAOmB,CAAA,CAAO,IAAIjB,CAAA,GAAI,KAAK6B,KAAA,IAGjC/B,CAAA,IACHmB,CAAA,CAAOjB,CAAA,EAAGF,CAAA,GJ3BQ,QI+BfA,CAAA,IAEA,KAAIwC,GAAA,KACHvC,CAAA,IACH,KAAIqG,GAAA,CAAiBhD,IAAA,CAAKrD,CAAA,GAE3BmD,CAAA,CAAc,MAEhB;AAAA,GAQAL,CAAA,CAAc8C,SAAA,CAAU0D,WAAA,GAAc,UAAUvJ,CAAA;EAC3C,KAAIwC,GAAA,KAIP,KAAIH,GAAA,IAAU,GACVrC,CAAA,IAAU,KAAIqG,GAAA,CAAkB/C,IAAA,CAAKtD,CAAA,GACzCoD,CAAA,CAAc,MAEhB;AAAA,GAYAL,CAAA,CAAc8C,SAAA,CAAUC,MAAA,GAAShD,CAAA,EA8F7B1C,CAAA,GAAgB,IAadE,CAAA,GACa,qBAAXkJ,OAAA,GACJA,OAAA,CAAQ3D,SAAA,CAAUqB,IAAA,CAAKuC,IAAA,CAAKD,OAAA,CAAQE,OAAA,MACpCC,UAAA,EAuBEpJ,CAAA,GAAY,SAAAqJ,CAAC5J,CAAA,EAAGC,CAAA;EAAA,OAAMD,CAAA,CAACwC,GAAA,CAAAJ,GAAA,GAAiBnC,CAAA,CAACuC,GAAA,CAAAJ,GAAc;AAAA,GA8B7DmB,CAAA,CAAOC,GAAA,GAAkB,GCxOnBhD,CAAA,GAAgB,+BAalBC,CAAA,GAAa,GA+IXC,CAAA,GAAaiF,CAAA,EAAiB,IAC9BhF,CAAA,GAAoBgF,CAAA,EAAiB,ICzKhC/E,CAAA,GAAI;AAAA,SAAAmC,CAAA,IAAA8G,SAAA,EAAA/G,CAAA,IAAAgH,QAAA,EAAApB,CAAA,IAAAqB,YAAA,EAAApB,CAAA,IAAAqB,aAAA,EAAAzI,CAAA,IAAA0I,aAAA,EAAArH,CAAA,IAAAsH,SAAA,EAAA3I,CAAA,IAAAX,CAAA,EAAA6H,CAAA,IAAA0B,OAAA,EAAAhK,CAAA,IAAAmJ,cAAA,EAAArJ,CAAA,IAAAmK,OAAA,EAAA9B,CAAA,IAAAxC,MAAA,EAAAlB,CAAA,IAAAyF,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export default function (_) {\n  return arguments.length ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1]) : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]];\n}", "map": {"version": 3, "names": ["_", "arguments", "length", "cover", "isNaN", "_x0", "undefined", "_y0", "_x1", "_y1"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-quadtree/src/extent.js"], "sourcesContent": ["export default function(_) {\n  return arguments.length\n      ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1])\n      : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]];\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,OAAOC,SAAS,CAACC,MAAM,GACjB,IAAI,CAACC,KAAK,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACxDI,KAAK,CAAC,IAAI,CAACC,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC,CAAC,IAAI,CAACD,GAAG,EAAE,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;AAClF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
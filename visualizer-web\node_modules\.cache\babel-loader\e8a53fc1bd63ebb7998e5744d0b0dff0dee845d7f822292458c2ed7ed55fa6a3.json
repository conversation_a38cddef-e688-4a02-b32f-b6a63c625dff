{"ast": null, "code": "import tree_add, { addAll as tree_addAll } from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport { findAllWithinRadius as tree_findAllWithinRadius } from \"./findAll.js\";\nimport tree_remove, { removeAll as tree_removeAll } from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, { defaultX } from \"./x.js\";\nimport tree_y, { defaultY } from \"./y.js\";\nimport tree_z, { defaultZ } from \"./z.js\";\nexport default function octree(nodes, x, y, z) {\n  var tree = new Octree(x == null ? defaultX : x, y == null ? defaultY : y, z == null ? defaultZ : z, Na<PERSON>, Na<PERSON>, NaN, Na<PERSON>, Na<PERSON>, Na<PERSON>);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\nfunction Octree(x, y, z, x0, y0, z0, x1, y1, z1) {\n  this._x = x;\n  this._y = y;\n  this._z = z;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._z0 = z0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._z1 = z1;\n  this._root = undefined;\n}\nfunction leaf_copy(leaf) {\n  var copy = {\n      data: leaf.data\n    },\n    next = copy;\n  while (leaf = leaf.next) next = next.next = {\n    data: leaf.data\n  };\n  return copy;\n}\nvar treeProto = octree.prototype = Octree.prototype;\ntreeProto.copy = function () {\n  var copy = new Octree(this._x, this._y, this._z, this._x0, this._y0, this._z0, this._x1, this._y1, this._z1),\n    node = this._root,\n    nodes,\n    child;\n  if (!node) return copy;\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n  nodes = [{\n    source: node,\n    target: copy._root = new Array(8)\n  }];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 8; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({\n          source: child,\n          target: node.target[i] = new Array(8)\n        });else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n  return copy;\n};\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.findAllWithinRadius = tree_findAllWithinRadius;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\ntreeProto.z = tree_z;", "map": {"version": 3, "names": ["tree_add", "addAll", "tree_addAll", "tree_cover", "tree_data", "tree_extent", "tree_find", "findAllWithinRadius", "tree_findAllWithinRadius", "tree_remove", "removeAll", "tree_removeAll", "tree_root", "tree_size", "tree_visit", "tree_visitAfter", "tree_x", "defaultX", "tree_y", "defaultY", "tree_z", "defaultZ", "octree", "nodes", "x", "y", "z", "tree", "Octree", "NaN", "x0", "y0", "z0", "x1", "y1", "z1", "_x", "_y", "_z", "_x0", "_y0", "_z0", "_x1", "_y1", "_z1", "_root", "undefined", "leaf_copy", "leaf", "copy", "data", "next", "treeProto", "prototype", "node", "child", "length", "source", "target", "Array", "pop", "i", "push", "add", "cover", "extent", "find", "remove", "root", "size", "visit", "visitAfter"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/octree.js"], "sourcesContent": ["import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport { findAllWithinRadius as tree_findAllWithinRadius } from \"./findAll.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\nimport tree_y, {defaultY} from \"./y.js\";\nimport tree_z, {defaultZ} from \"./z.js\";\n\nexport default function octree(nodes, x, y, z) {\n  var tree = new Octree(x == null ? defaultX : x, y == null ? defaultY : y, z == null ? defaultZ : z, Na<PERSON>, Na<PERSON>, NaN, Na<PERSON>, Na<PERSON>, Na<PERSON>);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Octree(x, y, z, x0, y0, z0, x1, y1, z1) {\n  this._x = x;\n  this._y = y;\n  this._z = z;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._z0 = z0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._z1 = z1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = octree.prototype = Octree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Octree(this._x, this._y, this._z, this._x0, this._y0, this._z0, this._x1, this._y1, this._z1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(8)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 8; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(8)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.findAllWithinRadius = tree_findAllWithinRadius;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\ntreeProto.z = tree_z;\n"], "mappings": "AAAA,OAAOA,QAAQ,IAAGC,MAAM,IAAIC,WAAW,QAAO,UAAU;AACxD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,mBAAmB,IAAIC,wBAAwB,QAAQ,cAAc;AAC9E,OAAOC,WAAW,IAAGC,SAAS,IAAIC,cAAc,QAAO,aAAa;AACpE,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,OAAOC,MAAM,IAAGC,QAAQ,QAAO,QAAQ;AACvC,OAAOC,MAAM,IAAGC,QAAQ,QAAO,QAAQ;AACvC,OAAOC,MAAM,IAAGC,QAAQ,QAAO,QAAQ;AAEvC,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAIC,IAAI,GAAG,IAAIC,MAAM,CAACJ,CAAC,IAAI,IAAI,GAAGP,QAAQ,GAAGO,CAAC,EAAEC,CAAC,IAAI,IAAI,GAAGN,QAAQ,GAAGM,CAAC,EAAEC,CAAC,IAAI,IAAI,GAAGL,QAAQ,GAAGK,CAAC,EAAEG,GAAG,EAAEA,GAAG,EAAEA,GAAG,EAAEA,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC;EACjI,OAAON,KAAK,IAAI,IAAI,GAAGI,IAAI,GAAGA,IAAI,CAAC1B,MAAM,CAACsB,KAAK,CAAC;AAClD;AAEA,SAASK,MAAMA,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC/C,IAAI,CAACC,EAAE,GAAGZ,CAAC;EACX,IAAI,CAACa,EAAE,GAAGZ,CAAC;EACX,IAAI,CAACa,EAAE,GAAGZ,CAAC;EACX,IAAI,CAACa,GAAG,GAAGT,EAAE;EACb,IAAI,CAACU,GAAG,GAAGT,EAAE;EACb,IAAI,CAACU,GAAG,GAAGT,EAAE;EACb,IAAI,CAACU,GAAG,GAAGT,EAAE;EACb,IAAI,CAACU,GAAG,GAAGT,EAAE;EACb,IAAI,CAACU,GAAG,GAAGT,EAAE;EACb,IAAI,CAACU,KAAK,GAAGC,SAAS;AACxB;AAEA,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,IAAI,GAAG;MAACC,IAAI,EAAEF,IAAI,CAACE;IAAI,CAAC;IAAEC,IAAI,GAAGF,IAAI;EACzC,OAAOD,IAAI,GAAGA,IAAI,CAACG,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACA,IAAI,GAAG;IAACD,IAAI,EAAEF,IAAI,CAACE;EAAI,CAAC;EAC7D,OAAOD,IAAI;AACb;AAEA,IAAIG,SAAS,GAAG9B,MAAM,CAAC+B,SAAS,GAAGzB,MAAM,CAACyB,SAAS;AAEnDD,SAAS,CAACH,IAAI,GAAG,YAAW;EAC1B,IAAIA,IAAI,GAAG,IAAIrB,MAAM,CAAC,IAAI,CAACQ,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;IACxGU,IAAI,GAAG,IAAI,CAACT,KAAK;IACjBtB,KAAK;IACLgC,KAAK;EAET,IAAI,CAACD,IAAI,EAAE,OAAOL,IAAI;EAEtB,IAAI,CAACK,IAAI,CAACE,MAAM,EAAE,OAAOP,IAAI,CAACJ,KAAK,GAAGE,SAAS,CAACO,IAAI,CAAC,EAAEL,IAAI;EAE3D1B,KAAK,GAAG,CAAC;IAACkC,MAAM,EAAEH,IAAI;IAAEI,MAAM,EAAET,IAAI,CAACJ,KAAK,GAAG,IAAIc,KAAK,CAAC,CAAC;EAAC,CAAC,CAAC;EAC3D,OAAOL,IAAI,GAAG/B,KAAK,CAACqC,GAAG,CAAC,CAAC,EAAE;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,IAAIN,KAAK,GAAGD,IAAI,CAACG,MAAM,CAACI,CAAC,CAAC,EAAE;QAC1B,IAAIN,KAAK,CAACC,MAAM,EAAEjC,KAAK,CAACuC,IAAI,CAAC;UAACL,MAAM,EAAEF,KAAK;UAAEG,MAAM,EAAEJ,IAAI,CAACI,MAAM,CAACG,CAAC,CAAC,GAAG,IAAIF,KAAK,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,KAChFL,IAAI,CAACI,MAAM,CAACG,CAAC,CAAC,GAAGd,SAAS,CAACQ,KAAK,CAAC;MACxC;IACF;EACF;EAEA,OAAON,IAAI;AACb,CAAC;AAEDG,SAAS,CAACW,GAAG,GAAG/D,QAAQ;AACxBoD,SAAS,CAACnD,MAAM,GAAGC,WAAW;AAC9BkD,SAAS,CAACY,KAAK,GAAG7D,UAAU;AAC5BiD,SAAS,CAACF,IAAI,GAAG9C,SAAS;AAC1BgD,SAAS,CAACa,MAAM,GAAG5D,WAAW;AAC9B+C,SAAS,CAACc,IAAI,GAAG5D,SAAS;AAC1B8C,SAAS,CAAC7C,mBAAmB,GAAGC,wBAAwB;AACxD4C,SAAS,CAACe,MAAM,GAAG1D,WAAW;AAC9B2C,SAAS,CAAC1C,SAAS,GAAGC,cAAc;AACpCyC,SAAS,CAACgB,IAAI,GAAGxD,SAAS;AAC1BwC,SAAS,CAACiB,IAAI,GAAGxD,SAAS;AAC1BuC,SAAS,CAACkB,KAAK,GAAGxD,UAAU;AAC5BsC,SAAS,CAACmB,UAAU,GAAGxD,eAAe;AACtCqC,SAAS,CAAC5B,CAAC,GAAGR,MAAM;AACpBoC,SAAS,CAAC3B,CAAC,GAAGP,MAAM;AACpBkC,SAAS,CAAC1B,CAAC,GAAGN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
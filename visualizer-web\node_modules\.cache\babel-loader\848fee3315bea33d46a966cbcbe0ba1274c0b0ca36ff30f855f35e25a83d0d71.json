{"ast": null, "code": "module.exports = function getVariableName(index) {\n  if (index === 0) return 'x';\n  if (index === 1) return 'y';\n  if (index === 2) return 'z';\n  return 'c' + (index + 1);\n};", "map": {"version": 3, "names": ["module", "exports", "getVariableName", "index"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/getVariableName.js"], "sourcesContent": ["module.exports = function getVariableName(index) {\n  if (index === 0) return 'x';\n  if (index === 1) return 'y';\n  if (index === 2) return 'z';\n  return 'c' + (index + 1);\n};"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;EAC3B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;EAC3B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;EAC3B,OAAO,GAAG,IAAIA,KAAK,GAAG,CAAC,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "export function defaultY(d) {\n  return d[1];\n}\nexport default function (_) {\n  return arguments.length ? (this._y = _, this) : this._y;\n}", "map": {"version": 3, "names": ["defaultY", "d", "_", "arguments", "length", "_y"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/y.js"], "sourcesContent": ["export function defaultY(d) {\n  return d[1];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._y = _, this) : this._y;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,eAAe,UAASC,CAAC,EAAE;EACzB,OAAOC,SAAS,CAACC,MAAM,IAAI,IAAI,CAACC,EAAE,GAAGH,CAAC,EAAE,IAAI,IAAI,IAAI,CAACG,EAAE;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
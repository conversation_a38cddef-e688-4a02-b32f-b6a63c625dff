{"ast": null, "code": "import { Timer } from \"./timer.js\";\nexport default function (callback, delay, time) {\n  var t = new Timer();\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}", "map": {"version": 3, "names": ["Timer", "callback", "delay", "time", "t", "restart", "elapsed", "stop"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-timer/src/timeout.js"], "sourcesContent": ["import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAEhC,eAAe,UAASC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,IAAIC,CAAC,GAAG,IAAIJ,KAAK,CAAD,CAAC;EACjBE,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,KAAK;EAClCE,CAAC,CAACC,OAAO,CAACC,OAAO,IAAI;IACnBF,CAAC,CAACG,IAAI,CAAC,CAAC;IACRN,QAAQ,CAACK,OAAO,GAAGJ,KAAK,CAAC;EAC3B,CAAC,EAAEA,KAAK,EAAEC,IAAI,CAAC;EACf,OAAOC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
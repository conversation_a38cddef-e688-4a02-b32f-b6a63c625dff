{"ast": null, "code": "const createPatternBuilder = require('./createPatternBuilder');\nmodule.exports = generateCreateBodyFunction;\nmodule.exports.generateCreateBodyFunctionBody = generateCreateBodyFunctionBody;\n\n// InlineTransform: getVectorCode\nmodule.exports.getVectorCode = getVectorCode;\n// InlineTransform: getBodyCode\nmodule.exports.getBodyCode = getBodyCode;\n// InlineTransformExport: module.exports = function() { return Body; }\n\nfunction generateCreateBodyFunction(dimension, debugSetters) {\n  let code = generateCreateBodyFunctionBody(dimension, debugSetters);\n  let {\n    Body\n  } = new Function(code)();\n  return Body;\n}\nfunction generateCreateBodyFunctionBody(dimension, debugSetters) {\n  let code = `\n${getVectorCode(dimension, debugSetters)}\n${getBodyCode(dimension, debugSetters)}\nreturn {Body: Body, Vector: Vector};\n`;\n  return code;\n}\nfunction getBodyCode(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let variableList = pattern('{var}', {\n    join: ', '\n  });\n  return `\nfunction Body(${variableList}) {\n  this.isPinned = false;\n  this.pos = new Vector(${variableList});\n  this.force = new Vector();\n  this.velocity = new Vector();\n  this.mass = 1;\n\n  this.springCount = 0;\n  this.springLength = 0;\n}\n\nBody.prototype.reset = function() {\n  this.force.reset();\n  this.springCount = 0;\n  this.springLength = 0;\n}\n\nBody.prototype.setPosition = function (${variableList}) {\n  ${pattern('this.pos.{var} = {var} || 0;', {\n    indent: 2\n  })}\n};`;\n}\nfunction getVectorCode(dimension, debugSetters) {\n  let pattern = createPatternBuilder(dimension);\n  let setters = '';\n  if (debugSetters) {\n    setters = `${pattern(\"\\n\\\n   var v{var};\\n\\\nObject.defineProperty(this, '{var}', {\\n\\\n  set: function(v) { \\n\\\n    if (!Number.isFinite(v)) throw new Error('Cannot set non-numbers to {var}');\\n\\\n    v{var} = v; \\n\\\n  },\\n\\\n  get: function() { return v{var}; }\\n\\\n});\")}`;\n  }\n  let variableList = pattern('{var}', {\n    join: ', '\n  });\n  return `function Vector(${variableList}) {\n  ${setters}\n    if (typeof arguments[0] === 'object') {\n      // could be another vector\n      let v = arguments[0];\n      ${pattern('if (!Number.isFinite(v.{var})) throw new Error(\"Expected value is not a finite number at Vector constructor ({var})\");', {\n    indent: 4\n  })}\n      ${pattern('this.{var} = v.{var};', {\n    indent: 4\n  })}\n    } else {\n      ${pattern('this.{var} = typeof {var} === \"number\" ? {var} : 0;', {\n    indent: 4\n  })}\n    }\n  }\n  \n  Vector.prototype.reset = function () {\n    ${pattern('this.{var} = ', {\n    join: ''\n  })}0;\n  };`;\n}", "map": {"version": 3, "names": ["createPatternBuilder", "require", "module", "exports", "generateCreateBodyFunction", "generateCreateBodyFunctionBody", "getVectorCode", "getBodyCode", "dimension", "debugSetters", "code", "Body", "Function", "pattern", "variableList", "join", "indent", "setters"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/generateCreateBody.js"], "sourcesContent": ["\nconst createPatternBuilder = require('./createPatternBuilder');\n\nmodule.exports = generateCreateBodyFunction;\nmodule.exports.generateCreateBodyFunctionBody = generateCreateBodyFunctionBody;\n\n// InlineTransform: getVectorCode\nmodule.exports.getVectorCode = getVectorCode;\n// InlineTransform: getBodyCode\nmodule.exports.getBodyCode = getBodyCode;\n// InlineTransformExport: module.exports = function() { return Body; }\n\nfunction generateCreateBodyFunction(dimension, debugSetters) {\n  let code = generateCreateBodyFunctionBody(dimension, debugSetters);\n  let {Body} = (new Function(code))();\n  return Body;\n}\n\nfunction generateCreateBodyFunctionBody(dimension, debugSetters) {\n  let code = `\n${getVectorCode(dimension, debugSetters)}\n${getBodyCode(dimension, debugSetters)}\nreturn {Body: Body, Vector: Vector};\n`;\n  return code;\n}\n\nfunction getBodyCode(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let variableList = pattern('{var}', {join: ', '});\n  return `\nfunction Body(${variableList}) {\n  this.isPinned = false;\n  this.pos = new Vector(${variableList});\n  this.force = new Vector();\n  this.velocity = new Vector();\n  this.mass = 1;\n\n  this.springCount = 0;\n  this.springLength = 0;\n}\n\nBody.prototype.reset = function() {\n  this.force.reset();\n  this.springCount = 0;\n  this.springLength = 0;\n}\n\nBody.prototype.setPosition = function (${variableList}) {\n  ${pattern('this.pos.{var} = {var} || 0;', {indent: 2})}\n};`;\n}\n\nfunction getVectorCode(dimension, debugSetters) {\n  let pattern = createPatternBuilder(dimension);\n  let setters = '';\n  if (debugSetters) {\n    setters = `${pattern(\"\\n\\\n   var v{var};\\n\\\nObject.defineProperty(this, '{var}', {\\n\\\n  set: function(v) { \\n\\\n    if (!Number.isFinite(v)) throw new Error('Cannot set non-numbers to {var}');\\n\\\n    v{var} = v; \\n\\\n  },\\n\\\n  get: function() { return v{var}; }\\n\\\n});\")}`;\n  }\n\n  let variableList = pattern('{var}', {join: ', '});\n  return `function Vector(${variableList}) {\n  ${setters}\n    if (typeof arguments[0] === 'object') {\n      // could be another vector\n      let v = arguments[0];\n      ${pattern('if (!Number.isFinite(v.{var})) throw new Error(\"Expected value is not a finite number at Vector constructor ({var})\");', {indent: 4})}\n      ${pattern('this.{var} = v.{var};', {indent: 4})}\n    } else {\n      ${pattern('this.{var} = typeof {var} === \"number\" ? {var} : 0;', {indent: 4})}\n    }\n  }\n  \n  Vector.prototype.reset = function () {\n    ${pattern('this.{var} = ', {join: ''})}0;\n  };`;\n}"], "mappings": "AACA,MAAMA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE9DC,MAAM,CAACC,OAAO,GAAGC,0BAA0B;AAC3CF,MAAM,CAACC,OAAO,CAACE,8BAA8B,GAAGA,8BAA8B;;AAE9E;AACAH,MAAM,CAACC,OAAO,CAACG,aAAa,GAAGA,aAAa;AAC5C;AACAJ,MAAM,CAACC,OAAO,CAACI,WAAW,GAAGA,WAAW;AACxC;;AAEA,SAASH,0BAA0BA,CAACI,SAAS,EAAEC,YAAY,EAAE;EAC3D,IAAIC,IAAI,GAAGL,8BAA8B,CAACG,SAAS,EAAEC,YAAY,CAAC;EAClE,IAAI;IAACE;EAAI,CAAC,GAAI,IAAIC,QAAQ,CAACF,IAAI,CAAC,CAAE,CAAC;EACnC,OAAOC,IAAI;AACb;AAEA,SAASN,8BAA8BA,CAACG,SAAS,EAAEC,YAAY,EAAE;EAC/D,IAAIC,IAAI,GAAG;AACb,EAAEJ,aAAa,CAACE,SAAS,EAAEC,YAAY,CAAC;AACxC,EAAEF,WAAW,CAACC,SAAS,EAAEC,YAAY,CAAC;AACtC;AACA,CAAC;EACC,OAAOC,IAAI;AACb;AAEA,SAASH,WAAWA,CAACC,SAAS,EAAE;EAC9B,IAAIK,OAAO,GAAGb,oBAAoB,CAACQ,SAAS,CAAC;EAC7C,IAAIM,YAAY,GAAGD,OAAO,CAAC,OAAO,EAAE;IAACE,IAAI,EAAE;EAAI,CAAC,CAAC;EACjD,OAAO;AACT,gBAAgBD,YAAY;AAC5B;AACA,0BAA0BA,YAAY;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyCA,YAAY;AACrD,IAAID,OAAO,CAAC,8BAA8B,EAAE;IAACG,MAAM,EAAE;EAAC,CAAC,CAAC;AACxD,GAAG;AACH;AAEA,SAASV,aAAaA,CAACE,SAAS,EAAEC,YAAY,EAAE;EAC9C,IAAII,OAAO,GAAGb,oBAAoB,CAACQ,SAAS,CAAC;EAC7C,IAAIS,OAAO,GAAG,EAAE;EAChB,IAAIR,YAAY,EAAE;IAChBQ,OAAO,GAAG,GAAGJ,OAAO,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,EAAE;EACL;EAEA,IAAIC,YAAY,GAAGD,OAAO,CAAC,OAAO,EAAE;IAACE,IAAI,EAAE;EAAI,CAAC,CAAC;EACjD,OAAO,mBAAmBD,YAAY;AACxC,IAAIG,OAAO;AACX;AACA;AACA;AACA,QAAQJ,OAAO,CAAC,wHAAwH,EAAE;IAACG,MAAM,EAAE;EAAC,CAAC,CAAC;AACtJ,QAAQH,OAAO,CAAC,uBAAuB,EAAE;IAACG,MAAM,EAAE;EAAC,CAAC,CAAC;AACrD;AACA,QAAQH,OAAO,CAAC,qDAAqD,EAAE;IAACG,MAAM,EAAE;EAAC,CAAC,CAAC;AACnF;AACA;AACA;AACA;AACA,MAAMH,OAAO,CAAC,eAAe,EAAE;IAACE,IAAI,EAAE;EAAE,CAAC,CAAC;AAC1C,KAAK;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
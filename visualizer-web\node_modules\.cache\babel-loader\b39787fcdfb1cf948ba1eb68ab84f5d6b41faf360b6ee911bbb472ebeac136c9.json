{"ast": null, "code": "function textRemove() {\n  this.textContent = \"\";\n}\nfunction textConstant(value) {\n  return function () {\n    this.textContent = value;\n  };\n}\nfunction textFunction(value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\nexport default function (value) {\n  return arguments.length ? this.each(value == null ? textRemove : (typeof value === \"function\" ? textFunction : textConstant)(value)) : this.node().textContent;\n}", "map": {"version": 3, "names": ["textRemove", "textContent", "textConstant", "value", "textFunction", "v", "apply", "arguments", "length", "each", "node"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selection/text.js"], "sourcesContent": ["function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n"], "mappings": "AAAA,SAASA,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACC,WAAW,GAAG,EAAE;AACvB;AAEA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChB,IAAI,CAACF,WAAW,GAAGE,KAAK;EAC1B,CAAC;AACH;AAEA,SAASC,YAAYA,CAACD,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChB,IAAIE,CAAC,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAI,CAACN,WAAW,GAAGI,CAAC,IAAI,IAAI,GAAG,EAAE,GAAGA,CAAC;EACvC,CAAC;AACH;AAEA,eAAe,UAASF,KAAK,EAAE;EAC7B,OAAOI,SAAS,CAACC,MAAM,GACjB,IAAI,CAACC,IAAI,CAACN,KAAK,IAAI,IAAI,GACnBH,UAAU,GAAG,CAAC,OAAOG,KAAK,KAAK,UAAU,GACzCC,YAAY,GACZF,YAAY,EAAEC,KAAK,CAAC,CAAC,GACzB,IAAI,CAACO,IAAI,CAAC,CAAC,CAACT,WAAW;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
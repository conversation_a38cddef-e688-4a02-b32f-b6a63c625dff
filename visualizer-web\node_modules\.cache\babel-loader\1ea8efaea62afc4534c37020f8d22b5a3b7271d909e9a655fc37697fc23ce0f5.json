{"ast": null, "code": "var getNative = require('./_getNative'),\n  root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\nmodule.exports = WeakMap;", "map": {"version": 3, "names": ["getNative", "require", "root", "WeakMap", "module", "exports"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/lodash/_WeakMap.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\n\nmodule.exports = WeakMap;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIE,OAAO,GAAGH,SAAS,CAACE,IAAI,EAAE,SAAS,CAAC;AAExCE,MAAM,CAACC,OAAO,GAAGF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
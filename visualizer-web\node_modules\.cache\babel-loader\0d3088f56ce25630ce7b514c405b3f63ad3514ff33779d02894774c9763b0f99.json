{"ast": null, "code": "export default function () {\n  var size = 0;\n  this.visit(function (node) {\n    if (!node.length) do ++size; while (node = node.next);\n  });\n  return size;\n}", "map": {"version": 3, "names": ["size", "visit", "node", "length", "next"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-quadtree/src/size.js"], "sourcesContent": ["export default function() {\n  var size = 0;\n  this.visit(function(node) {\n    if (!node.length) do ++size; while (node = node.next)\n  });\n  return size;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,IAAI,GAAG,CAAC;EACZ,IAAI,CAACC,KAAK,CAAC,UAASC,IAAI,EAAE;IACxB,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE,GAAG,EAAEH,IAAI,CAAC,QAAQE,IAAI,GAAGA,IAAI,CAACE,IAAI;EACtD,CAAC,CAAC;EACF,OAAOJ,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
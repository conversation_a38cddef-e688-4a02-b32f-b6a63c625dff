{"ast": null, "code": "export { now, timer, timerFlush } from \"./timer.js\";\nexport { default as timeout } from \"./timeout.js\";\nexport { default as interval } from \"./interval.js\";", "map": {"version": 3, "names": ["now", "timer", "timer<PERSON><PERSON><PERSON>", "default", "timeout", "interval"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-timer/src/index.js"], "sourcesContent": ["export {\n  now,\n  timer,\n  timerFlush\n} from \"./timer.js\";\n\nexport {\n  default as timeout\n} from \"./timeout.js\";\n\nexport {\n  default as interval\n} from \"./interval.js\";\n"], "mappings": "AAAA,SACEA,GAAG,EACHC,KAAK,EACLC,UAAU,QACL,YAAY;AAEnB,SACEC,OAAO,IAAIC,OAAO,QACb,cAAc;AAErB,SACED,OAAO,IAAIE,QAAQ,QACd,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
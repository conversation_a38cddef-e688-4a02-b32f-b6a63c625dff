{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"e5f5e0a1d99b31a354\", \"edf8e9bae4b374c476238b45\", \"edf8e9bae4b374c47631a354006d2c\", \"edf8e9c7e9c0a1d99b74c47631a354006d2c\", \"edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32\", \"f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32\", \"f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-single/Greens.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e5f5e0a1d99b31a354\",\n  \"edf8e9bae4b374c476238b45\",\n  \"edf8e9bae4b374c47631a354006d2c\",\n  \"edf8e9c7e9c0a1d99b74c47631a354006d2c\",\n  \"edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32\",\n  \"f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32\",\n  \"f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
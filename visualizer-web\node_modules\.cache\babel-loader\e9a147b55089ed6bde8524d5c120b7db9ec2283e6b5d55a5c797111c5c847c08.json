{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"ef8a62ffffff999999\", \"ca0020f4a582bababa404040\", \"ca0020f4a582ffffffbababa404040\", \"b2182bef8a62fddbc7e0e0e09999994d4d4d\", \"b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d\", \"b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d\", \"b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d\", \"67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a\", \"67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/diverging/RdGy.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ef8a62ffffff999999\",\n  \"ca0020f4a582bababa404040\",\n  \"ca0020f4a582ffffffbababa404040\",\n  \"b2182bef8a62fddbc7e0e0e09999994d4d4d\",\n  \"b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d\",\n  \"b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d\",\n  \"b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d\",\n  \"67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a\",\n  \"67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
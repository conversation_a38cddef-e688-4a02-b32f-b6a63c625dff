{"ast": null, "code": "module.exports = merge;\n\n/**\n * Augments `target` with properties in `options`. Does not override\n * target's properties if they are defined and matches expected type in \n * options\n *\n * @returns {Object} merged object\n */\nfunction merge(target, options) {\n  var key;\n  if (!target) {\n    target = {};\n  }\n  if (options) {\n    for (key in options) {\n      if (options.hasOwnProperty(key)) {\n        var targetHasIt = target.hasOwnProperty(key),\n          optionsValueType = typeof options[key],\n          shouldReplace = !targetHasIt || typeof target[key] !== optionsValueType;\n        if (shouldReplace) {\n          target[key] = options[key];\n        } else if (optionsValueType === 'object') {\n          // go deep, don't care about loops here, we are simple API!:\n          target[key] = merge(target[key], options[key]);\n        }\n      }\n    }\n  }\n  return target;\n}", "map": {"version": 3, "names": ["module", "exports", "merge", "target", "options", "key", "hasOwnProperty", "targetHasIt", "optionsValueType", "shouldReplace"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.merge/index.js"], "sourcesContent": ["module.exports = merge;\n\n/**\n * Augments `target` with properties in `options`. Does not override\n * target's properties if they are defined and matches expected type in \n * options\n *\n * @returns {Object} merged object\n */\nfunction merge(target, options) {\n  var key;\n  if (!target) { target = {}; }\n  if (options) {\n    for (key in options) {\n      if (options.hasOwnProperty(key)) {\n        var targetHasIt = target.hasOwnProperty(key),\n            optionsValueType = typeof options[key],\n            shouldReplace = !targetHasIt || (typeof target[key] !== optionsValueType);\n\n        if (shouldReplace) {\n          target[key] = options[key];\n        } else if (optionsValueType === 'object') {\n          // go deep, don't care about loops here, we are simple API!:\n          target[key] = merge(target[key], options[key]);\n        }\n      }\n    }\n  }\n\n  return target;\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,KAAK;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC9B,IAAIC,GAAG;EACP,IAAI,CAACF,MAAM,EAAE;IAAEA,MAAM,GAAG,CAAC,CAAC;EAAE;EAC5B,IAAIC,OAAO,EAAE;IACX,KAAKC,GAAG,IAAID,OAAO,EAAE;MACnB,IAAIA,OAAO,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;QAC/B,IAAIE,WAAW,GAAGJ,MAAM,CAACG,cAAc,CAACD,GAAG,CAAC;UACxCG,gBAAgB,GAAG,OAAOJ,OAAO,CAACC,GAAG,CAAC;UACtCI,aAAa,GAAG,CAACF,WAAW,IAAK,OAAOJ,MAAM,CAACE,GAAG,CAAC,KAAKG,gBAAiB;QAE7E,IAAIC,aAAa,EAAE;UACjBN,MAAM,CAACE,GAAG,CAAC,GAAGD,OAAO,CAACC,GAAG,CAAC;QAC5B,CAAC,MAAM,IAAIG,gBAAgB,KAAK,QAAQ,EAAE;UACxC;UACAL,MAAM,CAACE,GAAG,CAAC,GAAGH,KAAK,CAACC,MAAM,CAACE,GAAG,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC,CAAC;QAChD;MACF;IACF;EACF;EAEA,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
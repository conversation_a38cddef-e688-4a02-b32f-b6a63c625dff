[{"/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/index.tsx": "1", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/reportWebVitals.ts": "2", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/App.tsx": "3", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/DNAMatches/DNAMatches.tsx": "4", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Dashboard/Dashboard.tsx": "5", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Individuals/Individuals.tsx": "6", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Geography/Geography.tsx": "7", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Analytics/Analytics.tsx": "8", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Segments/Segments.tsx": "9", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/NetworkGraph/NetworkGraph.tsx": "10", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/Layout/Navbar.tsx": "11", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/Layout/Sidebar.tsx": "12", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/services/api.ts": "13", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/Dashboard/StatCard.tsx": "14", "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/NetworkGraph/ForceGraph3D.tsx": "15"}, {"size": 1605, "mtime": 1748173118158, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1748164625810, "results": "18", "hashOfConfig": "17"}, {"size": 2850, "mtime": 1748164365667, "results": "19", "hashOfConfig": "17"}, {"size": 6202, "mtime": 1748176297614, "results": "20", "hashOfConfig": "17"}, {"size": 7657, "mtime": 1748173700602, "results": "21", "hashOfConfig": "17"}, {"size": 12379, "mtime": 1748201237647, "results": "22", "hashOfConfig": "17"}, {"size": 5441, "mtime": 1748179378309, "results": "23", "hashOfConfig": "17"}, {"size": 19248, "mtime": 1748199794721, "results": "24", "hashOfConfig": "17"}, {"size": 14641, "mtime": 1748199794724, "results": "25", "hashOfConfig": "17"}, {"size": 8210, "mtime": 1748164528512, "results": "26", "hashOfConfig": "17"}, {"size": 1762, "mtime": 1748164424674, "results": "27", "hashOfConfig": "17"}, {"size": 4904, "mtime": 1748164445400, "results": "28", "hashOfConfig": "17"}, {"size": 5908, "mtime": 1748200537919, "results": "29", "hashOfConfig": "17"}, {"size": 2539, "mtime": 1748164484865, "results": "30", "hashOfConfig": "17"}, {"size": 5874, "mtime": 1748177151656, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "95cefh", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/index.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/reportWebVitals.ts", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/App.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/DNAMatches/DNAMatches.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Dashboard/Dashboard.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Individuals/Individuals.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Geography/Geography.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Analytics/Analytics.tsx", ["77", "78", "79", "80", "81", "82"], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/Segments/Segments.tsx", ["83", "84"], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/pages/NetworkGraph/NetworkGraph.tsx", ["85"], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/Layout/Navbar.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/Layout/Sidebar.tsx", ["86"], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/services/api.ts", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/Dashboard/StatCard.tsx", [], [], "/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/src/components/NetworkGraph/ForceGraph3D.tsx", ["87"], [], {"ruleId": "88", "severity": 1, "message": "89", "line": 12, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 12, "endColumn": 9}, {"ruleId": "88", "severity": 1, "message": "92", "line": 35, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 35, "endColumn": 12}, {"ruleId": "88", "severity": 1, "message": "93", "line": 36, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 36, "endColumn": 7}, {"ruleId": "88", "severity": 1, "message": "94", "line": 37, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 37, "endColumn": 7}, {"ruleId": "88", "severity": 1, "message": "95", "line": 38, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 38, "endColumn": 12}, {"ruleId": "88", "severity": 1, "message": "96", "line": 143, "column": 11, "nodeType": "90", "messageId": "91", "endLine": 143, "endColumn": 34}, {"ruleId": "88", "severity": 1, "message": "92", "line": 38, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 38, "endColumn": 12}, {"ruleId": "88", "severity": 1, "message": "93", "line": 39, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 39, "endColumn": 7}, {"ruleId": "88", "severity": 1, "message": "97", "line": 1, "column": 27, "nodeType": "90", "messageId": "91", "endLine": 1, "endColumn": 36}, {"ruleId": "88", "severity": 1, "message": "98", "line": 17, "column": 18, "nodeType": "90", "messageId": "91", "endLine": 17, "endColumn": 26}, {"ruleId": "99", "severity": 1, "message": "100", "line": 155, "column": 24, "nodeType": "90", "endLine": 155, "endColumn": 31}, "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Area' is defined but never used.", "'AreaChart' is defined but never used.", "'individualsWithoutTrees' is assigned a value but never used.", "'useEffect' is defined but never used.", "'TreeIcon' is defined but never used.", "react-hooks/exhaustive-deps", "The ref value 'containerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'containerRef.current' to a variable inside the effect, and use that variable in the cleanup function."]
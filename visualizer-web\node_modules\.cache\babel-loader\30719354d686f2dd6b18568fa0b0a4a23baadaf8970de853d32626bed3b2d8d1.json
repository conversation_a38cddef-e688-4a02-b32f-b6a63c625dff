{"ast": null, "code": "import tree_add, { addAll as tree_addAll } from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, { removeAll as tree_removeAll } from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, { defaultX } from \"./x.js\";\nimport tree_y, { defaultY } from \"./y.js\";\nexport default function quadtree(nodes, x, y) {\n  var tree = new Quadtree(x == null ? defaultX : x, y == null ? defaultY : y, NaN, NaN, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n  this._x = x;\n  this._y = y;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._root = undefined;\n}\nfunction leaf_copy(leaf) {\n  var copy = {\n      data: leaf.data\n    },\n    next = copy;\n  while (leaf = leaf.next) next = next.next = {\n    data: leaf.data\n  };\n  return copy;\n}\nvar treeProto = quadtree.prototype = Quadtree.prototype;\ntreeProto.copy = function () {\n  var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1),\n    node = this._root,\n    nodes,\n    child;\n  if (!node) return copy;\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n  nodes = [{\n    source: node,\n    target: copy._root = new Array(4)\n  }];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 4; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({\n          source: child,\n          target: node.target[i] = new Array(4)\n        });else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n  return copy;\n};\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;", "map": {"version": 3, "names": ["tree_add", "addAll", "tree_addAll", "tree_cover", "tree_data", "tree_extent", "tree_find", "tree_remove", "removeAll", "tree_removeAll", "tree_root", "tree_size", "tree_visit", "tree_visitAfter", "tree_x", "defaultX", "tree_y", "defaultY", "quadtree", "nodes", "x", "y", "tree", "Quadtree", "NaN", "x0", "y0", "x1", "y1", "_x", "_y", "_x0", "_y0", "_x1", "_y1", "_root", "undefined", "leaf_copy", "leaf", "copy", "data", "next", "treeProto", "prototype", "node", "child", "length", "source", "target", "Array", "pop", "i", "push", "add", "cover", "extent", "find", "remove", "root", "size", "visit", "visitAfter"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-quadtree/src/quadtree.js"], "sourcesContent": ["import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\nimport tree_y, {defaultY} from \"./y.js\";\n\nexport default function quadtree(nodes, x, y) {\n  var tree = new Quadtree(x == null ? defaultX : x, y == null ? defaultY : y, NaN, NaN, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n  this._x = x;\n  this._y = y;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = quadtree.prototype = Quadtree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(4)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 4; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(4)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\n"], "mappings": "AAAA,OAAOA,QAAQ,IAAGC,MAAM,IAAIC,WAAW,QAAO,UAAU;AACxD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,IAAGC,SAAS,IAAIC,cAAc,QAAO,aAAa;AACpE,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,OAAOC,MAAM,IAAGC,QAAQ,QAAO,QAAQ;AACvC,OAAOC,MAAM,IAAGC,QAAQ,QAAO,QAAQ;AAEvC,eAAe,SAASC,QAAQA,CAACC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC5C,IAAIC,IAAI,GAAG,IAAIC,QAAQ,CAACH,CAAC,IAAI,IAAI,GAAGL,QAAQ,GAAGK,CAAC,EAAEC,CAAC,IAAI,IAAI,GAAGJ,QAAQ,GAAGI,CAAC,EAAEG,GAAG,EAAEA,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC;EAC/F,OAAOL,KAAK,IAAI,IAAI,GAAGG,IAAI,GAAGA,IAAI,CAACrB,MAAM,CAACkB,KAAK,CAAC;AAClD;AAEA,SAASI,QAAQA,CAACH,CAAC,EAAEC,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACtC,IAAI,CAACC,EAAE,GAAGT,CAAC;EACX,IAAI,CAACU,EAAE,GAAGT,CAAC;EACX,IAAI,CAACU,GAAG,GAAGN,EAAE;EACb,IAAI,CAACO,GAAG,GAAGN,EAAE;EACb,IAAI,CAACO,GAAG,GAAGN,EAAE;EACb,IAAI,CAACO,GAAG,GAAGN,EAAE;EACb,IAAI,CAACO,KAAK,GAAGC,SAAS;AACxB;AAEA,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,IAAI,GAAG;MAACC,IAAI,EAAEF,IAAI,CAACE;IAAI,CAAC;IAAEC,IAAI,GAAGF,IAAI;EACzC,OAAOD,IAAI,GAAGA,IAAI,CAACG,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACA,IAAI,GAAG;IAACD,IAAI,EAAEF,IAAI,CAACE;EAAI,CAAC;EAC7D,OAAOD,IAAI;AACb;AAEA,IAAIG,SAAS,GAAGxB,QAAQ,CAACyB,SAAS,GAAGpB,QAAQ,CAACoB,SAAS;AAEvDD,SAAS,CAACH,IAAI,GAAG,YAAW;EAC1B,IAAIA,IAAI,GAAG,IAAIhB,QAAQ,CAAC,IAAI,CAACM,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;IAC7EU,IAAI,GAAG,IAAI,CAACT,KAAK;IACjBhB,KAAK;IACL0B,KAAK;EAET,IAAI,CAACD,IAAI,EAAE,OAAOL,IAAI;EAEtB,IAAI,CAACK,IAAI,CAACE,MAAM,EAAE,OAAOP,IAAI,CAACJ,KAAK,GAAGE,SAAS,CAACO,IAAI,CAAC,EAAEL,IAAI;EAE3DpB,KAAK,GAAG,CAAC;IAAC4B,MAAM,EAAEH,IAAI;IAAEI,MAAM,EAAET,IAAI,CAACJ,KAAK,GAAG,IAAIc,KAAK,CAAC,CAAC;EAAC,CAAC,CAAC;EAC3D,OAAOL,IAAI,GAAGzB,KAAK,CAAC+B,GAAG,CAAC,CAAC,EAAE;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,IAAIN,KAAK,GAAGD,IAAI,CAACG,MAAM,CAACI,CAAC,CAAC,EAAE;QAC1B,IAAIN,KAAK,CAACC,MAAM,EAAE3B,KAAK,CAACiC,IAAI,CAAC;UAACL,MAAM,EAAEF,KAAK;UAAEG,MAAM,EAAEJ,IAAI,CAACI,MAAM,CAACG,CAAC,CAAC,GAAG,IAAIF,KAAK,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,KAChFL,IAAI,CAACI,MAAM,CAACG,CAAC,CAAC,GAAGd,SAAS,CAACQ,KAAK,CAAC;MACxC;IACF;EACF;EAEA,OAAON,IAAI;AACb,CAAC;AAEDG,SAAS,CAACW,GAAG,GAAGrD,QAAQ;AACxB0C,SAAS,CAACzC,MAAM,GAAGC,WAAW;AAC9BwC,SAAS,CAACY,KAAK,GAAGnD,UAAU;AAC5BuC,SAAS,CAACF,IAAI,GAAGpC,SAAS;AAC1BsC,SAAS,CAACa,MAAM,GAAGlD,WAAW;AAC9BqC,SAAS,CAACc,IAAI,GAAGlD,SAAS;AAC1BoC,SAAS,CAACe,MAAM,GAAGlD,WAAW;AAC9BmC,SAAS,CAAClC,SAAS,GAAGC,cAAc;AACpCiC,SAAS,CAACgB,IAAI,GAAGhD,SAAS;AAC1BgC,SAAS,CAACiB,IAAI,GAAGhD,SAAS;AAC1B+B,SAAS,CAACkB,KAAK,GAAGhD,UAAU;AAC5B8B,SAAS,CAACmB,UAAU,GAAGhD,eAAe;AACtC6B,SAAS,CAACtB,CAAC,GAAGN,MAAM;AACpB4B,SAAS,CAACrB,CAAC,GAAGL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
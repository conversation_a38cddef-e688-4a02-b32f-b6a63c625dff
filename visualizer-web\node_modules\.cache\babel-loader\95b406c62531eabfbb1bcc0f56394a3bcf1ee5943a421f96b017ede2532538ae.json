{"ast": null, "code": "export default function (x, y, z) {\n  if (isNaN(x = +x) || isNaN(y = +y) || isNaN(z = +z)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n    y0 = this._y0,\n    z0 = this._z0,\n    x1 = this._x1,\n    y1 = this._y1,\n    z1 = this._z1;\n\n  // If the octree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing octant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n    z1 = (z0 = Math.floor(z)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var t = x1 - x0 || 1,\n      node = this._root,\n      parent,\n      i;\n    while (x0 > x || x >= x1 || y0 > y || y >= y1 || z0 > z || z >= z1) {\n      i = (z < z0) << 2 | (y < y0) << 1 | x < x0;\n      parent = new Array(8), parent[i] = node, node = parent, t *= 2;\n      switch (i) {\n        case 0:\n          x1 = x0 + t, y1 = y0 + t, z1 = z0 + t;\n          break;\n        case 1:\n          x0 = x1 - t, y1 = y0 + t, z1 = z0 + t;\n          break;\n        case 2:\n          x1 = x0 + t, y0 = y1 - t, z1 = z0 + t;\n          break;\n        case 3:\n          x0 = x1 - t, y0 = y1 - t, z1 = z0 + t;\n          break;\n        case 4:\n          x1 = x0 + t, y1 = y0 + t, z0 = z1 - t;\n          break;\n        case 5:\n          x0 = x1 - t, y1 = y0 + t, z0 = z1 - t;\n          break;\n        case 6:\n          x1 = x0 + t, y0 = y1 - t, z0 = z1 - t;\n          break;\n        case 7:\n          x0 = x1 - t, y0 = y1 - t, z0 = z1 - t;\n          break;\n      }\n    }\n    if (this._root && this._root.length) this._root = node;\n  }\n  this._x0 = x0;\n  this._y0 = y0;\n  this._z0 = z0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._z1 = z1;\n  return this;\n}", "map": {"version": 3, "names": ["x", "y", "z", "isNaN", "x0", "_x0", "y0", "_y0", "z0", "_z0", "x1", "_x1", "y1", "_y1", "z1", "_z1", "Math", "floor", "t", "node", "_root", "parent", "i", "Array", "length"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-octree/src/cover.js"], "sourcesContent": ["export default function(x, y, z) {\n  if (isNaN(x = +x) || isNaN(y = +y) || isNaN(z = +z)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      y0 = this._y0,\n      z0 = this._z0,\n      x1 = this._x1,\n      y1 = this._y1,\n      z1 = this._z1;\n\n  // If the octree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing octant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n    z1 = (z0 = Math.floor(z)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var t = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1 || y0 > y || y >= y1 || z0 > z || z >= z1) {\n      i = (z < z0) << 2 | (y < y0) << 1 | (x < x0);\n      parent = new Array(8), parent[i] = node, node = parent, t *= 2;\n      switch (i) {\n        case 0: x1 = x0 + t, y1 = y0 + t, z1 = z0 + t; break;\n        case 1: x0 = x1 - t, y1 = y0 + t, z1 = z0 + t; break;\n        case 2: x1 = x0 + t, y0 = y1 - t, z1 = z0 + t; break;\n        case 3: x0 = x1 - t, y0 = y1 - t, z1 = z0 + t; break;\n        case 4: x1 = x0 + t, y1 = y0 + t, z0 = z1 - t; break;\n        case 5: x0 = x1 - t, y1 = y0 + t, z0 = z1 - t; break;\n        case 6: x1 = x0 + t, y0 = y1 - t, z0 = z1 - t; break;\n        case 7: x0 = x1 - t, y0 = y1 - t, z0 = z1 - t; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._y0 = y0;\n  this._z0 = z0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._z1 = z1;\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIC,KAAK,CAACH,CAAC,GAAG,CAACA,CAAC,CAAC,IAAIG,KAAK,CAACF,CAAC,GAAG,CAACA,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAElE,IAAIE,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;;EAEjB;EACA;EACA;EACA,IAAIZ,KAAK,CAACC,EAAE,CAAC,EAAE;IACbM,EAAE,GAAG,CAACN,EAAE,GAAGY,IAAI,CAACC,KAAK,CAACjB,CAAC,CAAC,IAAI,CAAC;IAC7BY,EAAE,GAAG,CAACN,EAAE,GAAGU,IAAI,CAACC,KAAK,CAAChB,CAAC,CAAC,IAAI,CAAC;IAC7Ba,EAAE,GAAG,CAACN,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAACf,CAAC,CAAC,IAAI,CAAC;EAC/B;;EAEA;EAAA,KACK;IACH,IAAIgB,CAAC,GAAGR,EAAE,GAAGN,EAAE,IAAI,CAAC;MAChBe,IAAI,GAAG,IAAI,CAACC,KAAK;MACjBC,MAAM;MACNC,CAAC;IAEL,OAAOlB,EAAE,GAAGJ,CAAC,IAAIA,CAAC,IAAIU,EAAE,IAAIJ,EAAE,GAAGL,CAAC,IAAIA,CAAC,IAAIW,EAAE,IAAIJ,EAAE,GAAGN,CAAC,IAAIA,CAAC,IAAIY,EAAE,EAAE;MAClEQ,CAAC,GAAG,CAACpB,CAAC,GAAGM,EAAE,KAAK,CAAC,GAAG,CAACP,CAAC,GAAGK,EAAE,KAAK,CAAC,GAAIN,CAAC,GAAGI,EAAG;MAC5CiB,MAAM,GAAG,IAAIE,KAAK,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACC,CAAC,CAAC,GAAGH,IAAI,EAAEA,IAAI,GAAGE,MAAM,EAAEH,CAAC,IAAI,CAAC;MAC9D,QAAQI,CAAC;QACP,KAAK,CAAC;UAAEZ,EAAE,GAAGN,EAAE,GAAGc,CAAC,EAAEN,EAAE,GAAGN,EAAE,GAAGY,CAAC,EAAEJ,EAAE,GAAGN,EAAE,GAAGU,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAEd,EAAE,GAAGM,EAAE,GAAGQ,CAAC,EAAEN,EAAE,GAAGN,EAAE,GAAGY,CAAC,EAAEJ,EAAE,GAAGN,EAAE,GAAGU,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAER,EAAE,GAAGN,EAAE,GAAGc,CAAC,EAAEZ,EAAE,GAAGM,EAAE,GAAGM,CAAC,EAAEJ,EAAE,GAAGN,EAAE,GAAGU,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAEd,EAAE,GAAGM,EAAE,GAAGQ,CAAC,EAAEZ,EAAE,GAAGM,EAAE,GAAGM,CAAC,EAAEJ,EAAE,GAAGN,EAAE,GAAGU,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAER,EAAE,GAAGN,EAAE,GAAGc,CAAC,EAAEN,EAAE,GAAGN,EAAE,GAAGY,CAAC,EAAEV,EAAE,GAAGM,EAAE,GAAGI,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAEd,EAAE,GAAGM,EAAE,GAAGQ,CAAC,EAAEN,EAAE,GAAGN,EAAE,GAAGY,CAAC,EAAEV,EAAE,GAAGM,EAAE,GAAGI,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAER,EAAE,GAAGN,EAAE,GAAGc,CAAC,EAAEZ,EAAE,GAAGM,EAAE,GAAGM,CAAC,EAAEV,EAAE,GAAGM,EAAE,GAAGI,CAAC;UAAE;QAC/C,KAAK,CAAC;UAAEd,EAAE,GAAGM,EAAE,GAAGQ,CAAC,EAAEZ,EAAE,GAAGM,EAAE,GAAGM,CAAC,EAAEV,EAAE,GAAGM,EAAE,GAAGI,CAAC;UAAE;MACjD;IACF;IAEA,IAAI,IAAI,CAACE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACI,MAAM,EAAE,IAAI,CAACJ,KAAK,GAAGD,IAAI;EACxD;EAEA,IAAI,CAACd,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"e9a3c9f7f7f7a1d76a\", \"d01c8bf1b6dab8e1864dac26\", \"d01c8bf1b6daf7f7f7b8e1864dac26\", \"c51b7de9a3c9fde0efe6f5d0a1d76a4d9221\", \"c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221\", \"c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221\", \"c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221\", \"8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419\", \"8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/diverging/PiYG.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"e9a3c9f7f7f7a1d76a\",\n  \"d01c8bf1b6dab8e1864dac26\",\n  \"d01c8bf1b6daf7f7f7b8e1864dac26\",\n  \"c51b7de9a3c9fde0efe6f5d0a1d76a4d9221\",\n  \"c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221\",\n  \"c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221\",\n  \"c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221\",\n  \"8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419\",\n  \"8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
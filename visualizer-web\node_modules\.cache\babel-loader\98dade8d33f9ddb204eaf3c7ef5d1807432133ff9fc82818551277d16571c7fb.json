{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"fff7bcfec44fd95f0e\", \"ffffd4fed98efe9929cc4c02\", \"ffffd4fed98efe9929d95f0e993404\", \"ffffd4fee391fec44ffe9929d95f0e993404\", \"ffffd4fee391fec44ffe9929ec7014cc4c028c2d04\", \"ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04\", \"ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-scale-chromatic/src/sequential-multi/YlOrBr.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fff7bcfec44fd95f0e\",\n  \"ffffd4fed98efe9929cc4c02\",\n  \"ffffd4fed98efe9929d95f0e993404\",\n  \"ffffd4fee391fec44ffe9929d95f0e993404\",\n  \"ffffd4fee391fec44ffe9929ec7014cc4c028c2d04\",\n  \"ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04\",\n  \"ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const createPatternBuilder = require('./createPatternBuilder');\nconst getVariableName = require('./getVariableName');\nmodule.exports = generateQuadTreeFunction;\nmodule.exports.generateQuadTreeFunctionBody = generateQuadTreeFunctionBody;\n\n// These exports are for InlineTransform tool.\n// InlineTransform: getInsertStackCode\nmodule.exports.getInsertStackCode = getInsertStackCode;\n// InlineTransform: getQuadNodeCode\nmodule.exports.getQuadNodeCode = getQuadNodeCode;\n// InlineTransform: isSamePosition\nmodule.exports.isSamePosition = isSamePosition;\n// InlineTransform: getChildBodyCode\nmodule.exports.getChildBodyCode = getChildBodyCode;\n// InlineTransform: setChildBodyCode\nmodule.exports.setChildBodyCode = setChildBodyCode;\nfunction generateQuadTreeFunction(dimension) {\n  let code = generateQuadTreeFunctionBody(dimension);\n  return new Function(code)();\n}\nfunction generateQuadTreeFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let quadCount = Math.pow(2, dimension);\n  let code = `\n${getInsertStackCode()}\n${getQuadNodeCode(dimension)}\n${isSamePosition(dimension)}\n${getChildBodyCode(dimension)}\n${setChildBodyCode(dimension)}\n\nfunction createQuadTree(options, random) {\n  options = options || {};\n  options.gravity = typeof options.gravity === 'number' ? options.gravity : -1;\n  options.theta = typeof options.theta === 'number' ? options.theta : 0.8;\n\n  var gravity = options.gravity;\n  var updateQueue = [];\n  var insertStack = new InsertStack();\n  var theta = options.theta;\n\n  var nodesCache = [];\n  var currentInCache = 0;\n  var root = newNode();\n\n  return {\n    insertBodies: insertBodies,\n\n    /**\n     * Gets root node if it is present\n     */\n    getRoot: function() {\n      return root;\n    },\n\n    updateBodyForce: update,\n\n    options: function(newOptions) {\n      if (newOptions) {\n        if (typeof newOptions.gravity === 'number') {\n          gravity = newOptions.gravity;\n        }\n        if (typeof newOptions.theta === 'number') {\n          theta = newOptions.theta;\n        }\n\n        return this;\n      }\n\n      return {\n        gravity: gravity,\n        theta: theta\n      };\n    }\n  };\n\n  function newNode() {\n    // To avoid pressure on GC we reuse nodes.\n    var node = nodesCache[currentInCache];\n    if (node) {\n${assignQuads('      node.')}\n      node.body = null;\n      node.mass = ${pattern('node.mass_{var} = ', {\n    join: ''\n  })}0;\n      ${pattern('node.min_{var} = node.max_{var} = ', {\n    join: ''\n  })}0;\n    } else {\n      node = new QuadNode();\n      nodesCache[currentInCache] = node;\n    }\n\n    ++currentInCache;\n    return node;\n  }\n\n  function update(sourceBody) {\n    var queue = updateQueue;\n    var v;\n    ${pattern('var d{var};', {\n    indent: 4\n  })}\n    var r; \n    ${pattern('var f{var} = 0;', {\n    indent: 4\n  })}\n    var queueLength = 1;\n    var shiftIdx = 0;\n    var pushIdx = 1;\n\n    queue[0] = root;\n\n    while (queueLength) {\n      var node = queue[shiftIdx];\n      var body = node.body;\n\n      queueLength -= 1;\n      shiftIdx += 1;\n      var differentBody = (body !== sourceBody);\n      if (body && differentBody) {\n        // If the current node is a leaf node (and it is not source body),\n        // calculate the force exerted by the current node on body, and add this\n        // amount to body's net force.\n        ${pattern('d{var} = body.pos.{var} - sourceBody.pos.{var};', {\n    indent: 8\n  })}\n        r = Math.sqrt(${pattern('d{var} * d{var}', {\n    join: ' + '\n  })});\n\n        if (r === 0) {\n          // Poor man's protection against zero distance.\n          ${pattern('d{var} = (random.nextDouble() - 0.5) / 50;', {\n    indent: 10\n  })}\n          r = Math.sqrt(${pattern('d{var} * d{var}', {\n    join: ' + '\n  })});\n        }\n\n        // This is standard gravitation force calculation but we divide\n        // by r^3 to save two operations when normalizing force vector.\n        v = gravity * body.mass * sourceBody.mass / (r * r * r);\n        ${pattern('f{var} += v * d{var};', {\n    indent: 8\n  })}\n      } else if (differentBody) {\n        // Otherwise, calculate the ratio s / r,  where s is the width of the region\n        // represented by the internal node, and r is the distance between the body\n        // and the node's center-of-mass\n        ${pattern('d{var} = node.mass_{var} / node.mass - sourceBody.pos.{var};', {\n    indent: 8\n  })}\n        r = Math.sqrt(${pattern('d{var} * d{var}', {\n    join: ' + '\n  })});\n\n        if (r === 0) {\n          // Sorry about code duplication. I don't want to create many functions\n          // right away. Just want to see performance first.\n          ${pattern('d{var} = (random.nextDouble() - 0.5) / 50;', {\n    indent: 10\n  })}\n          r = Math.sqrt(${pattern('d{var} * d{var}', {\n    join: ' + '\n  })});\n        }\n        // If s / r < θ, treat this internal node as a single body, and calculate the\n        // force it exerts on sourceBody, and add this amount to sourceBody's net force.\n        if ((node.max_${getVariableName(0)} - node.min_${getVariableName(0)}) / r < theta) {\n          // in the if statement above we consider node's width only\n          // because the region was made into square during tree creation.\n          // Thus there is no difference between using width or height.\n          v = gravity * node.mass * sourceBody.mass / (r * r * r);\n          ${pattern('f{var} += v * d{var};', {\n    indent: 10\n  })}\n        } else {\n          // Otherwise, run the procedure recursively on each of the current node's children.\n\n          // I intentionally unfolded this loop, to save several CPU cycles.\n${runRecursiveOnChildren()}\n        }\n      }\n    }\n\n    ${pattern('sourceBody.force.{var} += f{var};', {\n    indent: 4\n  })}\n  }\n\n  function insertBodies(bodies) {\n    ${pattern('var {var}min = Number.MAX_VALUE;', {\n    indent: 4\n  })}\n    ${pattern('var {var}max = Number.MIN_VALUE;', {\n    indent: 4\n  })}\n    var i = bodies.length;\n\n    // To reduce quad tree depth we are looking for exact bounding box of all particles.\n    while (i--) {\n      var pos = bodies[i].pos;\n      ${pattern('if (pos.{var} < {var}min) {var}min = pos.{var};', {\n    indent: 6\n  })}\n      ${pattern('if (pos.{var} > {var}max) {var}max = pos.{var};', {\n    indent: 6\n  })}\n    }\n\n    // Makes the bounds square.\n    var maxSideLength = -Infinity;\n    ${pattern('if ({var}max - {var}min > maxSideLength) maxSideLength = {var}max - {var}min ;', {\n    indent: 4\n  })}\n\n    currentInCache = 0;\n    root = newNode();\n    ${pattern('root.min_{var} = {var}min;', {\n    indent: 4\n  })}\n    ${pattern('root.max_{var} = {var}min + maxSideLength;', {\n    indent: 4\n  })}\n\n    i = bodies.length - 1;\n    if (i >= 0) {\n      root.body = bodies[i];\n    }\n    while (i--) {\n      insert(bodies[i], root);\n    }\n  }\n\n  function insert(newBody) {\n    insertStack.reset();\n    insertStack.push(root, newBody);\n\n    while (!insertStack.isEmpty()) {\n      var stackItem = insertStack.pop();\n      var node = stackItem.node;\n      var body = stackItem.body;\n\n      if (!node.body) {\n        // This is internal node. Update the total mass of the node and center-of-mass.\n        ${pattern('var {var} = body.pos.{var};', {\n    indent: 8\n  })}\n        node.mass += body.mass;\n        ${pattern('node.mass_{var} += body.mass * {var};', {\n    indent: 8\n  })}\n\n        // Recursively insert the body in the appropriate quadrant.\n        // But first find the appropriate quadrant.\n        var quadIdx = 0; // Assume we are in the 0's quad.\n        ${pattern('var min_{var} = node.min_{var};', {\n    indent: 8\n  })}\n        ${pattern('var max_{var} = (min_{var} + node.max_{var}) / 2;', {\n    indent: 8\n  })}\n\n${assignInsertionQuadIndex(8)}\n\n        var child = getChild(node, quadIdx);\n\n        if (!child) {\n          // The node is internal but this quadrant is not taken. Add\n          // subnode to it.\n          child = newNode();\n          ${pattern('child.min_{var} = min_{var};', {\n    indent: 10\n  })}\n          ${pattern('child.max_{var} = max_{var};', {\n    indent: 10\n  })}\n          child.body = body;\n\n          setChild(node, quadIdx, child);\n        } else {\n          // continue searching in this quadrant.\n          insertStack.push(child, body);\n        }\n      } else {\n        // We are trying to add to the leaf node.\n        // We have to convert current leaf into internal node\n        // and continue adding two nodes.\n        var oldBody = node.body;\n        node.body = null; // internal nodes do not cary bodies\n\n        if (isSamePosition(oldBody.pos, body.pos)) {\n          // Prevent infinite subdivision by bumping one node\n          // anywhere in this quadrant\n          var retriesCount = 3;\n          do {\n            var offset = random.nextDouble();\n            ${pattern('var d{var} = (node.max_{var} - node.min_{var}) * offset;', {\n    indent: 12\n  })}\n\n            ${pattern('oldBody.pos.{var} = node.min_{var} + d{var};', {\n    indent: 12\n  })}\n            retriesCount -= 1;\n            // Make sure we don't bump it out of the box. If we do, next iteration should fix it\n          } while (retriesCount > 0 && isSamePosition(oldBody.pos, body.pos));\n\n          if (retriesCount === 0 && isSamePosition(oldBody.pos, body.pos)) {\n            // This is very bad, we ran out of precision.\n            // if we do not return from the method we'll get into\n            // infinite loop here. So we sacrifice correctness of layout, and keep the app running\n            // Next layout iteration should get larger bounding box in the first step and fix this\n            return;\n          }\n        }\n        // Next iteration should subdivide node further.\n        insertStack.push(node, oldBody);\n        insertStack.push(node, body);\n      }\n    }\n  }\n}\nreturn createQuadTree;\n\n`;\n  return code;\n  function assignInsertionQuadIndex(indentCount) {\n    let insertionCode = [];\n    let indent = Array(indentCount + 1).join(' ');\n    for (let i = 0; i < dimension; ++i) {\n      insertionCode.push(indent + `if (${getVariableName(i)} > max_${getVariableName(i)}) {`);\n      insertionCode.push(indent + `  quadIdx = quadIdx + ${Math.pow(2, i)};`);\n      insertionCode.push(indent + `  min_${getVariableName(i)} = max_${getVariableName(i)};`);\n      insertionCode.push(indent + `  max_${getVariableName(i)} = node.max_${getVariableName(i)};`);\n      insertionCode.push(indent + `}`);\n    }\n    return insertionCode.join('\\n');\n    // if (x > max_x) { // somewhere in the eastern part.\n    //   quadIdx = quadIdx + 1;\n    //   left = right;\n    //   right = node.right;\n    // }\n  }\n  function runRecursiveOnChildren() {\n    let indent = Array(11).join(' ');\n    let recursiveCode = [];\n    for (let i = 0; i < quadCount; ++i) {\n      recursiveCode.push(indent + `if (node.quad${i}) {`);\n      recursiveCode.push(indent + `  queue[pushIdx] = node.quad${i};`);\n      recursiveCode.push(indent + `  queueLength += 1;`);\n      recursiveCode.push(indent + `  pushIdx += 1;`);\n      recursiveCode.push(indent + `}`);\n    }\n    return recursiveCode.join('\\n');\n    // if (node.quad0) {\n    //   queue[pushIdx] = node.quad0;\n    //   queueLength += 1;\n    //   pushIdx += 1;\n    // }\n  }\n  function assignQuads(indent) {\n    // this.quad0 = null;\n    // this.quad1 = null;\n    // this.quad2 = null;\n    // this.quad3 = null;\n    let quads = [];\n    for (let i = 0; i < quadCount; ++i) {\n      quads.push(`${indent}quad${i} = null;`);\n    }\n    return quads.join('\\n');\n  }\n}\nfunction isSamePosition(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  return `\n  function isSamePosition(point1, point2) {\n    ${pattern('var d{var} = Math.abs(point1.{var} - point2.{var});', {\n    indent: 2\n  })}\n  \n    return ${pattern('d{var} < 1e-8', {\n    join: ' && '\n  })};\n  }  \n`;\n}\nfunction setChildBodyCode(dimension) {\n  var quadCount = Math.pow(2, dimension);\n  return `\nfunction setChild(node, idx, child) {\n  ${setChildBody()}\n}`;\n  function setChildBody() {\n    let childBody = [];\n    for (let i = 0; i < quadCount; ++i) {\n      let prefix = i === 0 ? '  ' : '  else ';\n      childBody.push(`${prefix}if (idx === ${i}) node.quad${i} = child;`);\n    }\n    return childBody.join('\\n');\n    // if (idx === 0) node.quad0 = child;\n    // else if (idx === 1) node.quad1 = child;\n    // else if (idx === 2) node.quad2 = child;\n    // else if (idx === 3) node.quad3 = child;\n  }\n}\nfunction getChildBodyCode(dimension) {\n  return `function getChild(node, idx) {\n${getChildBody()}\n  return null;\n}`;\n  function getChildBody() {\n    let childBody = [];\n    let quadCount = Math.pow(2, dimension);\n    for (let i = 0; i < quadCount; ++i) {\n      childBody.push(`  if (idx === ${i}) return node.quad${i};`);\n    }\n    return childBody.join('\\n');\n    // if (idx === 0) return node.quad0;\n    // if (idx === 1) return node.quad1;\n    // if (idx === 2) return node.quad2;\n    // if (idx === 3) return node.quad3;\n  }\n}\nfunction getQuadNodeCode(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let quadCount = Math.pow(2, dimension);\n  var quadNodeCode = `\nfunction QuadNode() {\n  // body stored inside this node. In quad tree only leaf nodes (by construction)\n  // contain bodies:\n  this.body = null;\n\n  // Child nodes are stored in quads. Each quad is presented by number:\n  // 0 | 1\n  // -----\n  // 2 | 3\n${assignQuads('  this.')}\n\n  // Total mass of current node\n  this.mass = 0;\n\n  // Center of mass coordinates\n  ${pattern('this.mass_{var} = 0;', {\n    indent: 2\n  })}\n\n  // bounding box coordinates\n  ${pattern('this.min_{var} = 0;', {\n    indent: 2\n  })}\n  ${pattern('this.max_{var} = 0;', {\n    indent: 2\n  })}\n}\n`;\n  return quadNodeCode;\n  function assignQuads(indent) {\n    // this.quad0 = null;\n    // this.quad1 = null;\n    // this.quad2 = null;\n    // this.quad3 = null;\n    let quads = [];\n    for (let i = 0; i < quadCount; ++i) {\n      quads.push(`${indent}quad${i} = null;`);\n    }\n    return quads.join('\\n');\n  }\n}\nfunction getInsertStackCode() {\n  return `\n/**\n * Our implementation of QuadTree is non-recursive to avoid GC hit\n * This data structure represent stack of elements\n * which we are trying to insert into quad tree.\n */\nfunction InsertStack () {\n    this.stack = [];\n    this.popIdx = 0;\n}\n\nInsertStack.prototype = {\n    isEmpty: function() {\n        return this.popIdx === 0;\n    },\n    push: function (node, body) {\n        var item = this.stack[this.popIdx];\n        if (!item) {\n            // we are trying to avoid memory pressure: create new element\n            // only when absolutely necessary\n            this.stack[this.popIdx] = new InsertStackElement(node, body);\n        } else {\n            item.node = node;\n            item.body = body;\n        }\n        ++this.popIdx;\n    },\n    pop: function () {\n        if (this.popIdx > 0) {\n            return this.stack[--this.popIdx];\n        }\n    },\n    reset: function () {\n        this.popIdx = 0;\n    }\n};\n\nfunction InsertStackElement(node, body) {\n    this.node = node; // QuadTree node\n    this.body = body; // physical body which needs to be inserted to node\n}\n`;\n}", "map": {"version": 3, "names": ["createPatternBuilder", "require", "getVariableName", "module", "exports", "generateQuadTreeFunction", "generateQuadTreeFunctionBody", "getInsertStackCode", "getQuadNodeCode", "isSamePosition", "getChildBodyCode", "setChildBodyCode", "dimension", "code", "Function", "pattern", "quadCount", "Math", "pow", "assignQuads", "join", "indent", "runRecursiveOnChildren", "assignInsertionQuadIndex", "indentCount", "insertionCode", "Array", "i", "push", "recursiveCode", "quads", "setChildBody", "childBody", "prefix", "getChildBody", "quadNodeCode"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/ngraph.forcelayout/lib/codeGenerators/generateQuadTree.js"], "sourcesContent": ["const createPatternBuilder = require('./createPatternBuilder');\nconst getVariableName = require('./getVariableName');\n\nmodule.exports = generateQuadTreeFunction;\nmodule.exports.generateQuadTreeFunctionBody = generateQuadTreeFunctionBody;\n\n// These exports are for InlineTransform tool.\n// InlineTransform: getInsertStackCode\nmodule.exports.getInsertStackCode = getInsertStackCode;\n// InlineTransform: getQuadNodeCode\nmodule.exports.getQuadNodeCode = getQuadNodeCode;\n// InlineTransform: isSamePosition\nmodule.exports.isSamePosition = isSamePosition;\n// InlineTransform: getChildBodyCode\nmodule.exports.getChildBodyCode = getChildBodyCode;\n// InlineTransform: setChildBodyCode\nmodule.exports.setChildBodyCode = setChildBodyCode;\n\nfunction generateQuadTreeFunction(dimension) {\n  let code = generateQuadTreeFunctionBody(dimension);\n  return (new Function(code))();\n}\n\nfunction generateQuadTreeFunctionBody(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let quadCount = Math.pow(2, dimension);\n\n  let code = `\n${getInsertStackCode()}\n${getQuadNodeCode(dimension)}\n${isSamePosition(dimension)}\n${getChildBodyCode(dimension)}\n${setChildBodyCode(dimension)}\n\nfunction createQuadTree(options, random) {\n  options = options || {};\n  options.gravity = typeof options.gravity === 'number' ? options.gravity : -1;\n  options.theta = typeof options.theta === 'number' ? options.theta : 0.8;\n\n  var gravity = options.gravity;\n  var updateQueue = [];\n  var insertStack = new InsertStack();\n  var theta = options.theta;\n\n  var nodesCache = [];\n  var currentInCache = 0;\n  var root = newNode();\n\n  return {\n    insertBodies: insertBodies,\n\n    /**\n     * Gets root node if it is present\n     */\n    getRoot: function() {\n      return root;\n    },\n\n    updateBodyForce: update,\n\n    options: function(newOptions) {\n      if (newOptions) {\n        if (typeof newOptions.gravity === 'number') {\n          gravity = newOptions.gravity;\n        }\n        if (typeof newOptions.theta === 'number') {\n          theta = newOptions.theta;\n        }\n\n        return this;\n      }\n\n      return {\n        gravity: gravity,\n        theta: theta\n      };\n    }\n  };\n\n  function newNode() {\n    // To avoid pressure on GC we reuse nodes.\n    var node = nodesCache[currentInCache];\n    if (node) {\n${assignQuads('      node.')}\n      node.body = null;\n      node.mass = ${pattern('node.mass_{var} = ', {join: ''})}0;\n      ${pattern('node.min_{var} = node.max_{var} = ', {join: ''})}0;\n    } else {\n      node = new QuadNode();\n      nodesCache[currentInCache] = node;\n    }\n\n    ++currentInCache;\n    return node;\n  }\n\n  function update(sourceBody) {\n    var queue = updateQueue;\n    var v;\n    ${pattern('var d{var};', {indent: 4})}\n    var r; \n    ${pattern('var f{var} = 0;', {indent: 4})}\n    var queueLength = 1;\n    var shiftIdx = 0;\n    var pushIdx = 1;\n\n    queue[0] = root;\n\n    while (queueLength) {\n      var node = queue[shiftIdx];\n      var body = node.body;\n\n      queueLength -= 1;\n      shiftIdx += 1;\n      var differentBody = (body !== sourceBody);\n      if (body && differentBody) {\n        // If the current node is a leaf node (and it is not source body),\n        // calculate the force exerted by the current node on body, and add this\n        // amount to body's net force.\n        ${pattern('d{var} = body.pos.{var} - sourceBody.pos.{var};', {indent: 8})}\n        r = Math.sqrt(${pattern('d{var} * d{var}', {join: ' + '})});\n\n        if (r === 0) {\n          // Poor man's protection against zero distance.\n          ${pattern('d{var} = (random.nextDouble() - 0.5) / 50;', {indent: 10})}\n          r = Math.sqrt(${pattern('d{var} * d{var}', {join: ' + '})});\n        }\n\n        // This is standard gravitation force calculation but we divide\n        // by r^3 to save two operations when normalizing force vector.\n        v = gravity * body.mass * sourceBody.mass / (r * r * r);\n        ${pattern('f{var} += v * d{var};', {indent: 8})}\n      } else if (differentBody) {\n        // Otherwise, calculate the ratio s / r,  where s is the width of the region\n        // represented by the internal node, and r is the distance between the body\n        // and the node's center-of-mass\n        ${pattern('d{var} = node.mass_{var} / node.mass - sourceBody.pos.{var};', {indent: 8})}\n        r = Math.sqrt(${pattern('d{var} * d{var}', {join: ' + '})});\n\n        if (r === 0) {\n          // Sorry about code duplication. I don't want to create many functions\n          // right away. Just want to see performance first.\n          ${pattern('d{var} = (random.nextDouble() - 0.5) / 50;', {indent: 10})}\n          r = Math.sqrt(${pattern('d{var} * d{var}', {join: ' + '})});\n        }\n        // If s / r < θ, treat this internal node as a single body, and calculate the\n        // force it exerts on sourceBody, and add this amount to sourceBody's net force.\n        if ((node.max_${getVariableName(0)} - node.min_${getVariableName(0)}) / r < theta) {\n          // in the if statement above we consider node's width only\n          // because the region was made into square during tree creation.\n          // Thus there is no difference between using width or height.\n          v = gravity * node.mass * sourceBody.mass / (r * r * r);\n          ${pattern('f{var} += v * d{var};', {indent: 10})}\n        } else {\n          // Otherwise, run the procedure recursively on each of the current node's children.\n\n          // I intentionally unfolded this loop, to save several CPU cycles.\n${runRecursiveOnChildren()}\n        }\n      }\n    }\n\n    ${pattern('sourceBody.force.{var} += f{var};', {indent: 4})}\n  }\n\n  function insertBodies(bodies) {\n    ${pattern('var {var}min = Number.MAX_VALUE;', {indent: 4})}\n    ${pattern('var {var}max = Number.MIN_VALUE;', {indent: 4})}\n    var i = bodies.length;\n\n    // To reduce quad tree depth we are looking for exact bounding box of all particles.\n    while (i--) {\n      var pos = bodies[i].pos;\n      ${pattern('if (pos.{var} < {var}min) {var}min = pos.{var};', {indent: 6})}\n      ${pattern('if (pos.{var} > {var}max) {var}max = pos.{var};', {indent: 6})}\n    }\n\n    // Makes the bounds square.\n    var maxSideLength = -Infinity;\n    ${pattern('if ({var}max - {var}min > maxSideLength) maxSideLength = {var}max - {var}min ;', {indent: 4})}\n\n    currentInCache = 0;\n    root = newNode();\n    ${pattern('root.min_{var} = {var}min;', {indent: 4})}\n    ${pattern('root.max_{var} = {var}min + maxSideLength;', {indent: 4})}\n\n    i = bodies.length - 1;\n    if (i >= 0) {\n      root.body = bodies[i];\n    }\n    while (i--) {\n      insert(bodies[i], root);\n    }\n  }\n\n  function insert(newBody) {\n    insertStack.reset();\n    insertStack.push(root, newBody);\n\n    while (!insertStack.isEmpty()) {\n      var stackItem = insertStack.pop();\n      var node = stackItem.node;\n      var body = stackItem.body;\n\n      if (!node.body) {\n        // This is internal node. Update the total mass of the node and center-of-mass.\n        ${pattern('var {var} = body.pos.{var};', {indent: 8})}\n        node.mass += body.mass;\n        ${pattern('node.mass_{var} += body.mass * {var};', {indent: 8})}\n\n        // Recursively insert the body in the appropriate quadrant.\n        // But first find the appropriate quadrant.\n        var quadIdx = 0; // Assume we are in the 0's quad.\n        ${pattern('var min_{var} = node.min_{var};', {indent: 8})}\n        ${pattern('var max_{var} = (min_{var} + node.max_{var}) / 2;', {indent: 8})}\n\n${assignInsertionQuadIndex(8)}\n\n        var child = getChild(node, quadIdx);\n\n        if (!child) {\n          // The node is internal but this quadrant is not taken. Add\n          // subnode to it.\n          child = newNode();\n          ${pattern('child.min_{var} = min_{var};', {indent: 10})}\n          ${pattern('child.max_{var} = max_{var};', {indent: 10})}\n          child.body = body;\n\n          setChild(node, quadIdx, child);\n        } else {\n          // continue searching in this quadrant.\n          insertStack.push(child, body);\n        }\n      } else {\n        // We are trying to add to the leaf node.\n        // We have to convert current leaf into internal node\n        // and continue adding two nodes.\n        var oldBody = node.body;\n        node.body = null; // internal nodes do not cary bodies\n\n        if (isSamePosition(oldBody.pos, body.pos)) {\n          // Prevent infinite subdivision by bumping one node\n          // anywhere in this quadrant\n          var retriesCount = 3;\n          do {\n            var offset = random.nextDouble();\n            ${pattern('var d{var} = (node.max_{var} - node.min_{var}) * offset;', {indent: 12})}\n\n            ${pattern('oldBody.pos.{var} = node.min_{var} + d{var};', {indent: 12})}\n            retriesCount -= 1;\n            // Make sure we don't bump it out of the box. If we do, next iteration should fix it\n          } while (retriesCount > 0 && isSamePosition(oldBody.pos, body.pos));\n\n          if (retriesCount === 0 && isSamePosition(oldBody.pos, body.pos)) {\n            // This is very bad, we ran out of precision.\n            // if we do not return from the method we'll get into\n            // infinite loop here. So we sacrifice correctness of layout, and keep the app running\n            // Next layout iteration should get larger bounding box in the first step and fix this\n            return;\n          }\n        }\n        // Next iteration should subdivide node further.\n        insertStack.push(node, oldBody);\n        insertStack.push(node, body);\n      }\n    }\n  }\n}\nreturn createQuadTree;\n\n`;\n  return code;\n\n\n  function assignInsertionQuadIndex(indentCount) {\n    let insertionCode = [];\n    let indent = Array(indentCount + 1).join(' ');\n    for (let i = 0; i < dimension; ++i) {\n      insertionCode.push(indent + `if (${getVariableName(i)} > max_${getVariableName(i)}) {`);\n      insertionCode.push(indent + `  quadIdx = quadIdx + ${Math.pow(2, i)};`);\n      insertionCode.push(indent + `  min_${getVariableName(i)} = max_${getVariableName(i)};`);\n      insertionCode.push(indent + `  max_${getVariableName(i)} = node.max_${getVariableName(i)};`);\n      insertionCode.push(indent + `}`);\n    }\n    return insertionCode.join('\\n');\n    // if (x > max_x) { // somewhere in the eastern part.\n    //   quadIdx = quadIdx + 1;\n    //   left = right;\n    //   right = node.right;\n    // }\n  }\n\n  function runRecursiveOnChildren() {\n    let indent = Array(11).join(' ');\n    let recursiveCode = [];\n    for (let i = 0; i < quadCount; ++i) {\n      recursiveCode.push(indent + `if (node.quad${i}) {`);\n      recursiveCode.push(indent + `  queue[pushIdx] = node.quad${i};`);\n      recursiveCode.push(indent + `  queueLength += 1;`);\n      recursiveCode.push(indent + `  pushIdx += 1;`);\n      recursiveCode.push(indent + `}`);\n    }\n    return recursiveCode.join('\\n');\n    // if (node.quad0) {\n    //   queue[pushIdx] = node.quad0;\n    //   queueLength += 1;\n    //   pushIdx += 1;\n    // }\n  }\n\n  function assignQuads(indent) {\n    // this.quad0 = null;\n    // this.quad1 = null;\n    // this.quad2 = null;\n    // this.quad3 = null;\n    let quads = [];\n    for (let i = 0; i < quadCount; ++i) {\n      quads.push(`${indent}quad${i} = null;`);\n    }\n    return quads.join('\\n');\n  }\n}\n\nfunction isSamePosition(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  return `\n  function isSamePosition(point1, point2) {\n    ${pattern('var d{var} = Math.abs(point1.{var} - point2.{var});', {indent: 2})}\n  \n    return ${pattern('d{var} < 1e-8', {join: ' && '})};\n  }  \n`;\n}\n\nfunction setChildBodyCode(dimension) {\n  var quadCount = Math.pow(2, dimension);\n  return `\nfunction setChild(node, idx, child) {\n  ${setChildBody()}\n}`;\n  function setChildBody() {\n    let childBody = [];\n    for (let i = 0; i < quadCount; ++i) {\n      let prefix = (i === 0) ? '  ' : '  else ';\n      childBody.push(`${prefix}if (idx === ${i}) node.quad${i} = child;`);\n    }\n\n    return childBody.join('\\n');\n    // if (idx === 0) node.quad0 = child;\n    // else if (idx === 1) node.quad1 = child;\n    // else if (idx === 2) node.quad2 = child;\n    // else if (idx === 3) node.quad3 = child;\n  }\n}\n\nfunction getChildBodyCode(dimension) {\n  return `function getChild(node, idx) {\n${getChildBody()}\n  return null;\n}`;\n\n  function getChildBody() {\n    let childBody = [];\n    let quadCount = Math.pow(2, dimension);\n    for (let i = 0; i < quadCount; ++i) {\n      childBody.push(`  if (idx === ${i}) return node.quad${i};`);\n    }\n\n    return childBody.join('\\n');\n    // if (idx === 0) return node.quad0;\n    // if (idx === 1) return node.quad1;\n    // if (idx === 2) return node.quad2;\n    // if (idx === 3) return node.quad3;\n  }\n}\n\nfunction getQuadNodeCode(dimension) {\n  let pattern = createPatternBuilder(dimension);\n  let quadCount = Math.pow(2, dimension);\n  var quadNodeCode = `\nfunction QuadNode() {\n  // body stored inside this node. In quad tree only leaf nodes (by construction)\n  // contain bodies:\n  this.body = null;\n\n  // Child nodes are stored in quads. Each quad is presented by number:\n  // 0 | 1\n  // -----\n  // 2 | 3\n${assignQuads('  this.')}\n\n  // Total mass of current node\n  this.mass = 0;\n\n  // Center of mass coordinates\n  ${pattern('this.mass_{var} = 0;', {indent: 2})}\n\n  // bounding box coordinates\n  ${pattern('this.min_{var} = 0;', {indent: 2})}\n  ${pattern('this.max_{var} = 0;', {indent: 2})}\n}\n`;\n  return quadNodeCode;\n\n  function assignQuads(indent) {\n    // this.quad0 = null;\n    // this.quad1 = null;\n    // this.quad2 = null;\n    // this.quad3 = null;\n    let quads = [];\n    for (let i = 0; i < quadCount; ++i) {\n      quads.push(`${indent}quad${i} = null;`);\n    }\n    return quads.join('\\n');\n  }\n}\n\nfunction getInsertStackCode() {\n  return `\n/**\n * Our implementation of QuadTree is non-recursive to avoid GC hit\n * This data structure represent stack of elements\n * which we are trying to insert into quad tree.\n */\nfunction InsertStack () {\n    this.stack = [];\n    this.popIdx = 0;\n}\n\nInsertStack.prototype = {\n    isEmpty: function() {\n        return this.popIdx === 0;\n    },\n    push: function (node, body) {\n        var item = this.stack[this.popIdx];\n        if (!item) {\n            // we are trying to avoid memory pressure: create new element\n            // only when absolutely necessary\n            this.stack[this.popIdx] = new InsertStackElement(node, body);\n        } else {\n            item.node = node;\n            item.body = body;\n        }\n        ++this.popIdx;\n    },\n    pop: function () {\n        if (this.popIdx > 0) {\n            return this.stack[--this.popIdx];\n        }\n    },\n    reset: function () {\n        this.popIdx = 0;\n    }\n};\n\nfunction InsertStackElement(node, body) {\n    this.node = node; // QuadTree node\n    this.body = body; // physical body which needs to be inserted to node\n}\n`;\n}"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC9D,MAAMC,eAAe,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAEpDE,MAAM,CAACC,OAAO,GAAGC,wBAAwB;AACzCF,MAAM,CAACC,OAAO,CAACE,4BAA4B,GAAGA,4BAA4B;;AAE1E;AACA;AACAH,MAAM,CAACC,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AACtD;AACAJ,MAAM,CAACC,OAAO,CAACI,eAAe,GAAGA,eAAe;AAChD;AACAL,MAAM,CAACC,OAAO,CAACK,cAAc,GAAGA,cAAc;AAC9C;AACAN,MAAM,CAACC,OAAO,CAACM,gBAAgB,GAAGA,gBAAgB;AAClD;AACAP,MAAM,CAACC,OAAO,CAACO,gBAAgB,GAAGA,gBAAgB;AAElD,SAASN,wBAAwBA,CAACO,SAAS,EAAE;EAC3C,IAAIC,IAAI,GAAGP,4BAA4B,CAACM,SAAS,CAAC;EAClD,OAAQ,IAAIE,QAAQ,CAACD,IAAI,CAAC,CAAE,CAAC;AAC/B;AAEA,SAASP,4BAA4BA,CAACM,SAAS,EAAE;EAC/C,IAAIG,OAAO,GAAGf,oBAAoB,CAACY,SAAS,CAAC;EAC7C,IAAII,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,SAAS,CAAC;EAEtC,IAAIC,IAAI,GAAG;AACb,EAAEN,kBAAkB,CAAC,CAAC;AACtB,EAAEC,eAAe,CAACI,SAAS,CAAC;AAC5B,EAAEH,cAAc,CAACG,SAAS,CAAC;AAC3B,EAAEF,gBAAgB,CAACE,SAAS,CAAC;AAC7B,EAAED,gBAAgB,CAACC,SAAS,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAEO,WAAW,CAAC,aAAa,CAAC;AAC5B;AACA,oBAAoBJ,OAAO,CAAC,oBAAoB,EAAE;IAACK,IAAI,EAAE;EAAE,CAAC,CAAC;AAC7D,QAAQL,OAAO,CAAC,oCAAoC,EAAE;IAACK,IAAI,EAAE;EAAE,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAML,OAAO,CAAC,aAAa,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACzC;AACA,MAAMN,OAAO,CAAC,iBAAiB,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUN,OAAO,CAAC,iDAAiD,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACjF,wBAAwBN,OAAO,CAAC,iBAAiB,EAAE;IAACK,IAAI,EAAE;EAAK,CAAC,CAAC;AACjE;AACA;AACA;AACA,YAAYL,OAAO,CAAC,4CAA4C,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AAC/E,0BAA0BN,OAAO,CAAC,iBAAiB,EAAE;IAACK,IAAI,EAAE;EAAK,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,UAAUL,OAAO,CAAC,uBAAuB,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA,UAAUN,OAAO,CAAC,8DAA8D,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC9F,wBAAwBN,OAAO,CAAC,iBAAiB,EAAE;IAACK,IAAI,EAAE;EAAK,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA,YAAYL,OAAO,CAAC,4CAA4C,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AAC/E,0BAA0BN,OAAO,CAAC,iBAAiB,EAAE;IAACK,IAAI,EAAE;EAAK,CAAC,CAAC;AACnE;AACA;AACA;AACA,wBAAwBlB,eAAe,CAAC,CAAC,CAAC,eAAeA,eAAe,CAAC,CAAC,CAAC;AAC3E;AACA;AACA;AACA;AACA,YAAYa,OAAO,CAAC,uBAAuB,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA,EAAEC,sBAAsB,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA,MAAMP,OAAO,CAAC,mCAAmC,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/D;AACA;AACA;AACA,MAAMN,OAAO,CAAC,kCAAkC,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC9D,MAAMN,OAAO,CAAC,kCAAkC,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,QAAQN,OAAO,CAAC,iDAAiD,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/E,QAAQN,OAAO,CAAC,iDAAiD,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/E;AACA;AACA;AACA;AACA,MAAMN,OAAO,CAAC,gFAAgF,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC5G;AACA;AACA;AACA,MAAMN,OAAO,CAAC,4BAA4B,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACxD,MAAMN,OAAO,CAAC,4CAA4C,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUN,OAAO,CAAC,6BAA6B,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC7D;AACA,UAAUN,OAAO,CAAC,uCAAuC,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACvE;AACA;AACA;AACA;AACA,UAAUN,OAAO,CAAC,iCAAiC,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACjE,UAAUN,OAAO,CAAC,mDAAmD,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACnF;AACA,EAAEE,wBAAwB,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYR,OAAO,CAAC,8BAA8B,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AACjE,YAAYN,OAAO,CAAC,8BAA8B,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAcN,OAAO,CAAC,0DAA0D,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AAC/F;AACA,cAAcN,OAAO,CAAC,8CAA8C,EAAE;IAACM,MAAM,EAAE;EAAE,CAAC,CAAC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;EACC,OAAOR,IAAI;EAGX,SAASU,wBAAwBA,CAACC,WAAW,EAAE;IAC7C,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIJ,MAAM,GAAGK,KAAK,CAACF,WAAW,GAAG,CAAC,CAAC,CAACJ,IAAI,CAAC,GAAG,CAAC;IAC7C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,SAAS,EAAE,EAAEe,CAAC,EAAE;MAClCF,aAAa,CAACG,IAAI,CAACP,MAAM,GAAG,OAAOnB,eAAe,CAACyB,CAAC,CAAC,UAAUzB,eAAe,CAACyB,CAAC,CAAC,KAAK,CAAC;MACvFF,aAAa,CAACG,IAAI,CAACP,MAAM,GAAG,yBAAyBJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAES,CAAC,CAAC,GAAG,CAAC;MACvEF,aAAa,CAACG,IAAI,CAACP,MAAM,GAAG,SAASnB,eAAe,CAACyB,CAAC,CAAC,UAAUzB,eAAe,CAACyB,CAAC,CAAC,GAAG,CAAC;MACvFF,aAAa,CAACG,IAAI,CAACP,MAAM,GAAG,SAASnB,eAAe,CAACyB,CAAC,CAAC,eAAezB,eAAe,CAACyB,CAAC,CAAC,GAAG,CAAC;MAC5FF,aAAa,CAACG,IAAI,CAACP,MAAM,GAAG,GAAG,CAAC;IAClC;IACA,OAAOI,aAAa,CAACL,IAAI,CAAC,IAAI,CAAC;IAC/B;IACA;IACA;IACA;IACA;EACF;EAEA,SAASE,sBAAsBA,CAAA,EAAG;IAChC,IAAID,MAAM,GAAGK,KAAK,CAAC,EAAE,CAAC,CAACN,IAAI,CAAC,GAAG,CAAC;IAChC,IAAIS,aAAa,GAAG,EAAE;IACtB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,EAAE,EAAEW,CAAC,EAAE;MAClCE,aAAa,CAACD,IAAI,CAACP,MAAM,GAAG,gBAAgBM,CAAC,KAAK,CAAC;MACnDE,aAAa,CAACD,IAAI,CAACP,MAAM,GAAG,+BAA+BM,CAAC,GAAG,CAAC;MAChEE,aAAa,CAACD,IAAI,CAACP,MAAM,GAAG,qBAAqB,CAAC;MAClDQ,aAAa,CAACD,IAAI,CAACP,MAAM,GAAG,iBAAiB,CAAC;MAC9CQ,aAAa,CAACD,IAAI,CAACP,MAAM,GAAG,GAAG,CAAC;IAClC;IACA,OAAOQ,aAAa,CAACT,IAAI,CAAC,IAAI,CAAC;IAC/B;IACA;IACA;IACA;IACA;EACF;EAEA,SAASD,WAAWA,CAACE,MAAM,EAAE;IAC3B;IACA;IACA;IACA;IACA,IAAIS,KAAK,GAAG,EAAE;IACd,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,EAAE,EAAEW,CAAC,EAAE;MAClCG,KAAK,CAACF,IAAI,CAAC,GAAGP,MAAM,OAAOM,CAAC,UAAU,CAAC;IACzC;IACA,OAAOG,KAAK,CAACV,IAAI,CAAC,IAAI,CAAC;EACzB;AACF;AAEA,SAASX,cAAcA,CAACG,SAAS,EAAE;EACjC,IAAIG,OAAO,GAAGf,oBAAoB,CAACY,SAAS,CAAC;EAC7C,OAAO;AACT;AACA,MAAMG,OAAO,CAAC,qDAAqD,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AACjF;AACA,aAAaN,OAAO,CAAC,eAAe,EAAE;IAACK,IAAI,EAAE;EAAM,CAAC,CAAC;AACrD;AACA,CAAC;AACD;AAEA,SAAST,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAII,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,SAAS,CAAC;EACtC,OAAO;AACT;AACA,IAAImB,YAAY,CAAC,CAAC;AAClB,EAAE;EACA,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,EAAE,EAAEW,CAAC,EAAE;MAClC,IAAIM,MAAM,GAAIN,CAAC,KAAK,CAAC,GAAI,IAAI,GAAG,SAAS;MACzCK,SAAS,CAACJ,IAAI,CAAC,GAAGK,MAAM,eAAeN,CAAC,cAAcA,CAAC,WAAW,CAAC;IACrE;IAEA,OAAOK,SAAS,CAACZ,IAAI,CAAC,IAAI,CAAC;IAC3B;IACA;IACA;IACA;EACF;AACF;AAEA,SAASV,gBAAgBA,CAACE,SAAS,EAAE;EACnC,OAAO;AACT,EAAEsB,YAAY,CAAC,CAAC;AAChB;AACA,EAAE;EAEA,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIF,SAAS,GAAG,EAAE;IAClB,IAAIhB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,SAAS,CAAC;IACtC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,EAAE,EAAEW,CAAC,EAAE;MAClCK,SAAS,CAACJ,IAAI,CAAC,iBAAiBD,CAAC,qBAAqBA,CAAC,GAAG,CAAC;IAC7D;IAEA,OAAOK,SAAS,CAACZ,IAAI,CAAC,IAAI,CAAC;IAC3B;IACA;IACA;IACA;EACF;AACF;AAEA,SAASZ,eAAeA,CAACI,SAAS,EAAE;EAClC,IAAIG,OAAO,GAAGf,oBAAoB,CAACY,SAAS,CAAC;EAC7C,IAAII,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,SAAS,CAAC;EACtC,IAAIuB,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAEhB,WAAW,CAAC,SAAS,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA,IAAIJ,OAAO,CAAC,sBAAsB,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAChD;AACA;AACA,IAAIN,OAAO,CAAC,qBAAqB,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/C,IAAIN,OAAO,CAAC,qBAAqB,EAAE;IAACM,MAAM,EAAE;EAAC,CAAC,CAAC;AAC/C;AACA,CAAC;EACC,OAAOc,YAAY;EAEnB,SAAShB,WAAWA,CAACE,MAAM,EAAE;IAC3B;IACA;IACA;IACA;IACA,IAAIS,KAAK,GAAG,EAAE;IACd,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,EAAE,EAAEW,CAAC,EAAE;MAClCG,KAAK,CAACF,IAAI,CAAC,GAAGP,MAAM,OAAOM,CAAC,UAAU,CAAC;IACzC;IACA,OAAOG,KAAK,CAACV,IAAI,CAAC,IAAI,CAAC;EACzB;AACF;AAEA,SAASb,kBAAkBA,CAAA,EAAG;EAC5B,OAAO;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
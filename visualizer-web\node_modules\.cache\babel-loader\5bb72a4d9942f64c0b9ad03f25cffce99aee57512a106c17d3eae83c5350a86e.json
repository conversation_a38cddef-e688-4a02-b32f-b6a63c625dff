{"ast": null, "code": "import array from \"./array.js\";\nimport { Selection, root } from \"./selection/index.js\";\nexport default function (selector) {\n  return typeof selector === \"string\" ? new Selection([document.querySelectorAll(selector)], [document.documentElement]) : new Selection([array(selector)], root);\n}", "map": {"version": 3, "names": ["array", "Selection", "root", "selector", "document", "querySelectorAll", "documentElement"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-selection/src/selectAll.js"], "sourcesContent": ["import array from \"./array.js\";\nimport {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([document.querySelectorAll(selector)], [document.documentElement])\n      : new Selection([array(selector)], root);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,SAAQC,SAAS,EAAEC,IAAI,QAAO,sBAAsB;AAEpD,eAAe,UAASC,QAAQ,EAAE;EAChC,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAC7B,IAAIF,SAAS,CAAC,CAACG,QAAQ,CAACC,gBAAgB,CAACF,QAAQ,CAAC,CAAC,EAAE,CAACC,QAAQ,CAACE,eAAe,CAAC,CAAC,GAChF,IAAIL,SAAS,CAAC,CAACD,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAED,IAAI,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
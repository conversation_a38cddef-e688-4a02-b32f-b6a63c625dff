{"ast": null, "code": "import tree_add, { addAll as tree_addAll } from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, { removeAll as tree_removeAll } from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, { defaultX } from \"./x.js\";\nexport default function binarytree(nodes, x) {\n  var tree = new Binarytree(x == null ? defaultX : x, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\nfunction Binarytree(x, x0, x1) {\n  this._x = x;\n  this._x0 = x0;\n  this._x1 = x1;\n  this._root = undefined;\n}\nfunction leaf_copy(leaf) {\n  var copy = {\n      data: leaf.data\n    },\n    next = copy;\n  while (leaf = leaf.next) next = next.next = {\n    data: leaf.data\n  };\n  return copy;\n}\nvar treeProto = binarytree.prototype = Binarytree.prototype;\ntreeProto.copy = function () {\n  var copy = new Binarytree(this._x, this._x0, this._x1),\n    node = this._root,\n    nodes,\n    child;\n  if (!node) return copy;\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n  nodes = [{\n    source: node,\n    target: copy._root = new Array(2)\n  }];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 2; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({\n          source: child,\n          target: node.target[i] = new Array(2)\n        });else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n  return copy;\n};\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;", "map": {"version": 3, "names": ["tree_add", "addAll", "tree_addAll", "tree_cover", "tree_data", "tree_extent", "tree_find", "tree_remove", "removeAll", "tree_removeAll", "tree_root", "tree_size", "tree_visit", "tree_visitAfter", "tree_x", "defaultX", "binarytree", "nodes", "x", "tree", "Binarytree", "NaN", "x0", "x1", "_x", "_x0", "_x1", "_root", "undefined", "leaf_copy", "leaf", "copy", "data", "next", "treeProto", "prototype", "node", "child", "length", "source", "target", "Array", "pop", "i", "push", "add", "cover", "extent", "find", "remove", "root", "size", "visit", "visitAfter"], "sources": ["/mnt/c/Users/<USER>/projects/myheritage/visualizer-web/node_modules/d3-binarytree/src/binarytree.js"], "sourcesContent": ["import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\n\nexport default function binarytree(nodes, x) {\n  var tree = new Binarytree(x == null ? defaultX : x, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Binarytree(x, x0, x1) {\n  this._x = x;\n  this._x0 = x0;\n  this._x1 = x1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = binarytree.prototype = Binarytree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Binarytree(this._x, this._x0, this._x1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(2)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 2; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(2)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;"], "mappings": "AAAA,OAAOA,QAAQ,IAAGC,MAAM,IAAIC,WAAW,QAAO,UAAU;AACxD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,IAAGC,SAAS,IAAIC,cAAc,QAAO,aAAa;AACpE,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,OAAOC,MAAM,IAAGC,QAAQ,QAAO,QAAQ;AAEvC,eAAe,SAASC,UAAUA,CAACC,KAAK,EAAEC,CAAC,EAAE;EAC3C,IAAIC,IAAI,GAAG,IAAIC,UAAU,CAACF,CAAC,IAAI,IAAI,GAAGH,QAAQ,GAAGG,CAAC,EAAEG,GAAG,EAAEA,GAAG,CAAC;EAC7D,OAAOJ,KAAK,IAAI,IAAI,GAAGE,IAAI,GAAGA,IAAI,CAAClB,MAAM,CAACgB,KAAK,CAAC;AAClD;AAEA,SAASG,UAAUA,CAACF,CAAC,EAAEI,EAAE,EAAEC,EAAE,EAAE;EAC7B,IAAI,CAACC,EAAE,GAAGN,CAAC;EACX,IAAI,CAACO,GAAG,GAAGH,EAAE;EACb,IAAI,CAACI,GAAG,GAAGH,EAAE;EACb,IAAI,CAACI,KAAK,GAAGC,SAAS;AACxB;AAEA,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,IAAI,GAAG;MAACC,IAAI,EAAEF,IAAI,CAACE;IAAI,CAAC;IAAEC,IAAI,GAAGF,IAAI;EACzC,OAAOD,IAAI,GAAGA,IAAI,CAACG,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACA,IAAI,GAAG;IAACD,IAAI,EAAEF,IAAI,CAACE;EAAI,CAAC;EAC7D,OAAOD,IAAI;AACb;AAEA,IAAIG,SAAS,GAAGlB,UAAU,CAACmB,SAAS,GAAGf,UAAU,CAACe,SAAS;AAE3DD,SAAS,CAACH,IAAI,GAAG,YAAW;EAC1B,IAAIA,IAAI,GAAG,IAAIX,UAAU,CAAC,IAAI,CAACI,EAAE,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;IAClDU,IAAI,GAAG,IAAI,CAACT,KAAK;IACjBV,KAAK;IACLoB,KAAK;EAET,IAAI,CAACD,IAAI,EAAE,OAAOL,IAAI;EAEtB,IAAI,CAACK,IAAI,CAACE,MAAM,EAAE,OAAOP,IAAI,CAACJ,KAAK,GAAGE,SAAS,CAACO,IAAI,CAAC,EAAEL,IAAI;EAE3Dd,KAAK,GAAG,CAAC;IAACsB,MAAM,EAAEH,IAAI;IAAEI,MAAM,EAAET,IAAI,CAACJ,KAAK,GAAG,IAAIc,KAAK,CAAC,CAAC;EAAC,CAAC,CAAC;EAC3D,OAAOL,IAAI,GAAGnB,KAAK,CAACyB,GAAG,CAAC,CAAC,EAAE;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,IAAIN,KAAK,GAAGD,IAAI,CAACG,MAAM,CAACI,CAAC,CAAC,EAAE;QAC1B,IAAIN,KAAK,CAACC,MAAM,EAAErB,KAAK,CAAC2B,IAAI,CAAC;UAACL,MAAM,EAAEF,KAAK;UAAEG,MAAM,EAAEJ,IAAI,CAACI,MAAM,CAACG,CAAC,CAAC,GAAG,IAAIF,KAAK,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,KAChFL,IAAI,CAACI,MAAM,CAACG,CAAC,CAAC,GAAGd,SAAS,CAACQ,KAAK,CAAC;MACxC;IACF;EACF;EAEA,OAAON,IAAI;AACb,CAAC;AAEDG,SAAS,CAACW,GAAG,GAAG7C,QAAQ;AACxBkC,SAAS,CAACjC,MAAM,GAAGC,WAAW;AAC9BgC,SAAS,CAACY,KAAK,GAAG3C,UAAU;AAC5B+B,SAAS,CAACF,IAAI,GAAG5B,SAAS;AAC1B8B,SAAS,CAACa,MAAM,GAAG1C,WAAW;AAC9B6B,SAAS,CAACc,IAAI,GAAG1C,SAAS;AAC1B4B,SAAS,CAACe,MAAM,GAAG1C,WAAW;AAC9B2B,SAAS,CAAC1B,SAAS,GAAGC,cAAc;AACpCyB,SAAS,CAACgB,IAAI,GAAGxC,SAAS;AAC1BwB,SAAS,CAACiB,IAAI,GAAGxC,SAAS;AAC1BuB,SAAS,CAACkB,KAAK,GAAGxC,UAAU;AAC5BsB,SAAS,CAACmB,UAAU,GAAGxC,eAAe;AACtCqB,SAAS,CAAChB,CAAC,GAAGJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}